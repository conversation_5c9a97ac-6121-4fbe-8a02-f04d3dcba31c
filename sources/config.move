module haedal::config {

    use sui::event;

    const FEE_DENOMINATOR: u64 = 1000_0000;
    const EConfigUnchanged: u64 = 1;
    const EFeeInvalid: u64 = 2;

    /// `StakingConfig` holds configuration for Staking, will be stored in `Staking`.
    public struct StakingConfig has store {
        /// Deposit fee rate, default is 0, reserved for future use
        deposit_fee: u64,
        /// Reward fee rate, default is 10 percent of rewards
        reward_fee: u64,
        /// Validator reward fee rate, take from the reward_fee, reserved for future use
        validator_reward_fee: u64,
        /// Unstake instant service fee rate
        service_fee: u64,
        /// Unstake normal wait time for claim
        withdraw_time_limit: u64,
        /// Validator count when calling do_stake
        validator_count: u64,
        /// Unstake walurs epoch is a built-in differentiator for walrus.system.epoch
        walrus_start_epoch: u32,
        /// Unstake walurs epoch start timestamp
        walrus_start_timestamp_ms: u64,
        /// Unstake walurs epoch duration time
        walrus_epoch_duration: u64
    }

    /// `StakingConfigCreated` is inited when the `StakingConfig` was created.
    public struct StakingConfigCreated has copy, drop {
        deposit_fee: u64,
        reward_fee: u64,
        validator_reward_fee: u64,
        service_fee: u64,
        withdraw_time_limit: u64,
        validator_count: u64,
        walrus_start_epoch: u32,
        walrus_start_timestamp_ms: u64,
        walrus_epoch_duration: u64
    }

    /// Staking config updated events.
    public struct StakingFeeConfigUpdated has copy, drop {
        name: vector<u8>,
        old: u64,
        new: u64,
    }

    public(package) fun new(
        deposit_fee: u64,
        reward_fee: u64,
        validator_reward_fee: u64,
        service_fee: u64,
        withdraw_time_limit: u64,
        validator_count: u64,
        walrus_start_epoch: u32,
        walrus_start_timestamp_ms: u64,
        walrus_epoch_duration: u64
    ) : StakingConfig {
        new_event(deposit_fee, reward_fee, validator_reward_fee, service_fee, withdraw_time_limit, validator_count,walrus_start_epoch,walrus_start_timestamp_ms,walrus_epoch_duration);
        StakingConfig {
            deposit_fee: deposit_fee,
            reward_fee: reward_fee,
            validator_reward_fee: validator_reward_fee,
            service_fee: service_fee,
            withdraw_time_limit: withdraw_time_limit,
            validator_count: validator_count,
            walrus_start_epoch:walrus_start_epoch,
            walrus_start_timestamp_ms:walrus_start_timestamp_ms,
            walrus_epoch_duration:walrus_epoch_duration,
        }
    }

    public(package) fun set_deposit_fee(config: &mut StakingConfig, deposit_fee: u64) {
        assert!(config.deposit_fee != deposit_fee, EConfigUnchanged);
        assert!(deposit_fee <= FEE_DENOMINATOR, EFeeInvalid);

        event::emit(StakingFeeConfigUpdated {
            name: b"DepositFeeUpdated",
            old: config.deposit_fee,
            new: deposit_fee,
        });
        config.deposit_fee = deposit_fee;
    }

    public(package) fun set_reward_fee(config: &mut StakingConfig, reward_fee: u64) {
        assert!(config.reward_fee != reward_fee, EConfigUnchanged);
        assert!(reward_fee <= FEE_DENOMINATOR, EFeeInvalid);

        event::emit(StakingFeeConfigUpdated {
            name: b"RewardFeeUpdated",
            old: config.reward_fee,
            new: reward_fee,
        });
        config.reward_fee = reward_fee;
    }

    public(package) fun set_validator_reward_fee(config: &mut StakingConfig, validator_reward_fee: u64) {
        assert!(config.validator_reward_fee != validator_reward_fee, EConfigUnchanged);

        event::emit(StakingFeeConfigUpdated {
            name: b"ValidatorRewardFeeUpdated",
            old: config.validator_reward_fee,
            new: validator_reward_fee,
        });
        config.validator_reward_fee = validator_reward_fee;
    }

    public(package) fun set_service_fee(config: &mut StakingConfig, service_fee: u64) {
        assert!(config.service_fee != service_fee, EConfigUnchanged);
        assert!(service_fee <= FEE_DENOMINATOR, EFeeInvalid);

        event::emit(StakingFeeConfigUpdated {
            name: b"ServiceFeeUpdated",
            old: config.service_fee,
            new: service_fee,
        });
        config.service_fee = service_fee;
    }

    public(package) fun set_withdraw_time_limit(config: &mut StakingConfig, withdraw_time_limit: u64) {
        assert!(config.withdraw_time_limit != withdraw_time_limit, EConfigUnchanged);

        event::emit(StakingFeeConfigUpdated {
            name: b"WithdrawTimeLimitUpdated",
            old: config.withdraw_time_limit,
            new: withdraw_time_limit,
        });
        config.withdraw_time_limit = withdraw_time_limit;
    }

    public(package) fun set_validator_count(config: &mut StakingConfig, validator_count: u64) {
        assert!(validator_count>0 && config.validator_count!=validator_count, EConfigUnchanged);

        event::emit(StakingFeeConfigUpdated {
            name: b"ValidatorCountUpdated",
            old: config.validator_count,
            new: validator_count,
        });
        config.validator_count = validator_count;
    }

    public(package) fun set_walrus_epoch_start(
        config: &mut StakingConfig, 
        start_epoch: u32,
        start_timestamp_ms: u64,
        epoch_duration: u64
    ) {
        assert!(config.walrus_start_epoch != start_epoch, EConfigUnchanged);
        assert!(config.walrus_start_timestamp_ms <= start_timestamp_ms, EConfigUnchanged);
        event::emit(StakingFeeConfigUpdated {
            name: b"WalrusEpochEtartUpdated",
            old: config.walrus_start_epoch as u64,
            new: start_epoch as u64,
        });
        event::emit(StakingFeeConfigUpdated {
            name: b"WalrusEpochEtartConfigUpdated",
            old: config.walrus_start_timestamp_ms,
            new: start_timestamp_ms,
        });
        event::emit(StakingFeeConfigUpdated {
            name: b"WalrusEpochDurationUpdated",
            old: config.walrus_epoch_duration,
            new: epoch_duration,
        });
        config.walrus_start_epoch = start_epoch;
        config.walrus_start_timestamp_ms = start_timestamp_ms;
        config.walrus_epoch_duration = epoch_duration;
    }

    public fun get_deposit_fee(config: &StakingConfig): u64 {
        config.deposit_fee
    }

    public fun get_reward_fee(config: &StakingConfig): u64 {
        config.reward_fee
    }

    public fun get_validator_reward_fee(config: &StakingConfig): u64 {
        config.validator_reward_fee
    }

    public fun get_service_fee(config: &StakingConfig): u64 {
        config.service_fee
    }

    public fun get_withdraw_time_limit(config: &StakingConfig): u64 {
        config.withdraw_time_limit
    }

    public fun get_validator_count(config: &StakingConfig): u64 {
        config.validator_count
    }

    public fun get_walrus_start_epoch(config: &StakingConfig): u32 {
        config.walrus_start_epoch
    }

    public fun get_walrus_start_timestamp_ms(config: &StakingConfig): u64 {
        config.walrus_start_timestamp_ms
    }

    public fun get_walrus_epoch_duration(config: &StakingConfig): u64 {
        config.walrus_epoch_duration
    }

    fun new_event(
        deposit_fee: u64,
        reward_fee: u64,
        validator_reward_fee: u64,
        service_fee: u64,
        withdraw_time_limit: u64,
        validator_count: u64,
        walrus_start_epoch: u32,
        walrus_start_timestamp_ms: u64,
        walrus_epoch_duration: u64
    ) {
        let scu = StakingConfigCreated {
            deposit_fee: deposit_fee,
            reward_fee: reward_fee,
            validator_reward_fee: validator_reward_fee,
            service_fee: service_fee,
            withdraw_time_limit: withdraw_time_limit,
            validator_count: validator_count,
            walrus_start_epoch:walrus_start_epoch,
            walrus_start_timestamp_ms:walrus_start_timestamp_ms,
            walrus_epoch_duration:walrus_epoch_duration,
        };
        event::emit(scu);
    }
}
