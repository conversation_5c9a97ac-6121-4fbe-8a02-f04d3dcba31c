/// Interface for users.
module haedal::interface {
    use sui::clock::{Clock};
    use sui::coin::{Coin};

    use wal::wal::WAL;
    use walrus::{
        staking::{Staking as WalStaking},
        system::{System}
    }; 

    use haedal::hawal::{HAWAL};
    use haedal::walstaking::{Self, Staking, UnstakeTicket};

    public entry fun request_stake(
        wal_staking: &mut WalStaking, 
        staking: &mut Staking, 
        input: Coin<WAL>, 
        validator: ID, 
        ctx: &mut TxContext
        ) {
        transfer::public_transfer(
            walstaking::request_stake_coin(wal_staking, staking, input, validator, ctx), 
            tx_context::sender(ctx)
        )
    }

    public entry fun request_unstake_instant(
        system:&mut System,
        wal_staking: &mut WalStaking,
        staking: &mut Staking, 
        clock: &Clock, 
        input: Coin<HAWAL>, 
        ctx: &mut TxContext
        ) {
        transfer::public_transfer(
            walstaking::request_unstake_instant(system, wal_staking, staking, clock, input, ctx),
            tx_context::sender(ctx)
        )
    }
    
    public entry fun request_unstake_delay(
        system:&mut System,
        wal_staking: &mut WalStaking,
        staking: &mut Staking, 
        clock: &Clock, 
        input: Coin<HAWAL>, 
        ctx: &mut TxContext
        ) {
        walstaking::request_withdraw_stake(system,wal_staking,staking, clock, input, ctx);
    }

    public entry fun claim(
        system: &mut System, 
        wal_staking: &mut WalStaking, 
        staking: &mut Staking, 
        clock: &Clock,
        ticket: UnstakeTicket, 
        ctx: &mut TxContext
        ) {
        transfer::public_transfer(
            walstaking::withdraw_stake(system, wal_staking, staking,clock, ticket, ctx), 
            tx_context::sender(ctx)
        )
    }
}
