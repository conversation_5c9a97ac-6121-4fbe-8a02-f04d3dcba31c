module haedal::vault {

    use sui::balance::{Self, Balance};

    public struct Vault<phantom T> has key, store {
        id: UID,
        cache_pool: Balance<T>,
    }

    public(package) fun new<T>(ctx: &mut TxContext) : Vault<T> {
        Vault {
            id: object::new(ctx),
            cache_pool: balance::zero(),
        }
    }

    public(package) fun deposit<T>(vault: &mut Vault<T>, input: Balance<T>) {
        balance::join(&mut vault.cache_pool, input);
    }

    public(package) fun withdraw<T>(vault: &mut Vault<T>, amount: u64) : Balance<T> {
        balance::split(&mut vault.cache_pool, amount)
    }

    public(package) fun withdraw_max<T>(vault: &mut Vault<T>, amount: u64) : (Balance<T>, u64) {
        let total_amount = balance::value(&vault.cache_pool);
        if (total_amount >= amount) {
            (balance::split(&mut vault.cache_pool, amount), 0)
        } else {
            (balance::split(&mut vault.cache_pool, total_amount), amount - total_amount)
        }
    }

    public(package) fun withdraw_all<T>(vault: &mut Vault<T>) : Balance<T> {
        let amount = balance::value(&vault.cache_pool);
        balance::split(&mut vault.cache_pool, amount)
    }

    public fun vault_amount<T>(vault: &Vault<T>) : u64 {
        balance::value(&vault.cache_pool)
    }

}
