/// Interface for robot.
module haedal::robot {
    // use sui::tx_context::{TxContext};

    use haedal::manage::{RobotCap};
    use haedal::walstaking::{Self, Staking};
    use haedal::config::{Self};
    use walrus::{
        staking::{Staking as WalStaking},
        system::{System}
    };

   public entry fun update_validator_rewards_v2(_: &RobotCap, staking: &mut Staking, system: &mut System, wal_staking: &mut WalStaking, validator: ID,ctx:&mut TxContext) {
        walstaking::assert_version(staking);
        walstaking::update_validator_rewards(staking, system, wal_staking, validator, ctx);
    }

    public entry fun sort_validators_v2(_: &RobotCap, staking: &mut Staking, validators: vector<ID>) {
        walstaking::assert_version(staking);
        walstaking::sort_validators(staking, validators);
    }

    public entry fun validator_offline_v2(
        _: &RobotCap,
        system: &System,
        wal_staking: &mut WalStaking,
        staking: &mut Staking,
        validator: ID,
        ctx: &mut TxContext
    ) {
        walstaking::assert_version(staking);
        walstaking::do_validator_offline(staking, system, wal_staking, validator, ctx);
    }
}
