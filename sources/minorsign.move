/// Interface for minor sign.
module haedal::minorsign {
    // use sui::tx_context::{TxContext};

    use haedal::manage::{MinorSignCap};
    use haedal::walstaking::{Self, Staking};
    use haedal::config::{Self};
    use walrus::{
        staking::{Staking as WalStaking},
        system::{System}
    };

   public entry fun set_withdraw_time_limit_v2(_: &MinorSignCap, staking: &mut Staking, withdraw_time_limit: u64) {
        walstaking::assert_version(staking);
        config::set_withdraw_time_limit(walstaking::get_config_mut(staking), withdraw_time_limit);
    }

    public entry fun set_validator_count_v2(_: &MinorSignCap, staking: &mut Staking, validator_count: u64) {
        walstaking::assert_version(staking);
        config::set_validator_count(walstaking::get_config_mut(staking), validator_count);
    }

    public entry fun set_active_validators_v2(_: &MinorSignCap, staking: &mut Staking, active_validators: vector<ID>) {
        walstaking::assert_version(staking);
        walstaking::set_active_validators(staking, active_validators);
    }

    public entry fun set_walrus_epoch_start_v2(
        _: &MinorSignCap, 
        staking: &mut Staking, 
        start_epoch: u32,
        start_timestamp_ms: u64,
        epoch_duration: u64
    ) {
        walstaking::assert_version(staking);
        config::set_walrus_epoch_start(walstaking::get_config_mut(staking), start_epoch, start_timestamp_ms, epoch_duration);
    }

    public entry fun toggle_stake_v2(_: &MinorSignCap, staking: &mut Staking, status: bool) {
        walstaking::assert_version(staking);
        walstaking::toggle_stake(staking, status);
    }
}
