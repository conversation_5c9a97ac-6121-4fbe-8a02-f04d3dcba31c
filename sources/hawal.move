module haedal::hawal {
    use sui::coin::{Self};
    use sui::url::{Self};

    public struct HAWAL has drop {}

    fun init(_witness: HAWAL, ctx: &mut TxContext) {
        let (treasury, metadata) = coin::create_currency(
            _witness,
            9,
            b"ha<PERSON><PERSON>",
            b"haWA<PERSON>",
            b"haWAL is a staking token of WAL",
            option::some(url::new_unsafe_from_bytes(b"https://assets.haedal.xyz/logos/hawal.svg")),
            ctx
        );
        transfer::public_freeze_object(metadata);
        transfer::public_transfer(treasury, tx_context::sender(ctx));
    }

    #[test_only]
    public fun init_hawal_for_test(ctx: &mut TxContext) {
        init(HAWAL{}, ctx);
    }
}
