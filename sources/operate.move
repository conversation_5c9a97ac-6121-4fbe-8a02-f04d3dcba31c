/// Interface for operator.
module haedal::operate {
    // use sui::tx_context::{TxContext};

    use haedal::manage::{OperatorCap};
    use haedal::walstaking::{Self, Staking};
    use haedal::config::{Self};
    use walrus::{
        staking::{Staking as WalStaking},
        system::{System}
    };

    public entry fun set_deposit_fee(_: &OperatorCap, staking: &mut Staking, deposit_fee: u64) {
        walstaking::assert_version(staking);
        config::set_deposit_fee(walstaking::get_config_mut(staking), deposit_fee);
    }

    public entry fun set_reward_fee(_: &OperatorCap, staking: &mut Staking, reward_fee: u64) {
        walstaking::assert_version(staking);
        config::set_reward_fee(walstaking::get_config_mut(staking), reward_fee);
    }

    public entry fun set_validator_reward_fee(_: &OperatorCap, staking: &mut Staking, validator_reward_fee: u64) {
        walstaking::assert_version(staking);
        config::set_validator_reward_fee(walstaking::get_config_mut(staking), validator_reward_fee);
    }

    public entry fun set_service_fee(_: &OperatorCap, staking: &mut Staking, service_fee: u64) {
        walstaking::assert_version(staking);
        config::set_service_fee(walstaking::get_config_mut(staking), service_fee);
    }

    public entry fun set_withdraw_time_limit(_: &OperatorCap, staking: &mut Staking, withdraw_time_limit: u64) {
        walstaking::assert_version(staking);
        config::set_withdraw_time_limit(walstaking::get_config_mut(staking), withdraw_time_limit);
    }

    public entry fun set_validator_count(_: &OperatorCap, staking: &mut Staking, validator_count: u64) {
        walstaking::assert_version(staking);
        config::set_validator_count(walstaking::get_config_mut(staking), validator_count);
    }

    public entry fun set_active_validators(_: &OperatorCap, staking: &mut Staking, active_validators: vector<ID>) {
        walstaking::assert_version(staking);
        walstaking::set_active_validators(staking, active_validators);
    }

    public entry fun set_walrus_epoch_start(
        _: &OperatorCap, 
        staking: &mut Staking, 
        start_epoch: u32,
        start_timestamp_ms: u64,
        epoch_duration: u64
    ) {
        walstaking::assert_version(staking);
        config::set_walrus_epoch_start(walstaking::get_config_mut(staking), start_epoch, start_timestamp_ms, epoch_duration);
    }

    public entry fun toggle_stake(_: &OperatorCap, staking: &mut Staking, status: bool) {
        walstaking::assert_version(staking);
        walstaking::toggle_stake(staking, status);
    }

    public entry fun toggle_unstake(_: &OperatorCap, staking: &mut Staking, status: bool) {
        walstaking::assert_version(staking);
        walstaking::toggle_unstake(staking, status);
    }

    public entry fun toggle_claim(_: &OperatorCap, staking: &mut Staking, status: bool) {
        walstaking::assert_version(staking);
        walstaking::toggle_claim(staking, status);
    }

    /// At the begining of every epoch, do below:
    /// 1. pause claim/stake/unstake
    /// 2. call `update_validator_rewards` for every validator separately(to avoid abort for update all the active_validators)
    /// 3. resume claim/stake/unstake
    public entry fun update_validator_rewards(_: &OperatorCap, staking: &mut Staking, system: &mut System, wal_staking: &mut WalStaking, validator: ID,ctx:&mut TxContext) {
        walstaking::assert_version(staking);
        walstaking::update_validator_rewards(staking, system, wal_staking, validator, ctx);
    }

    public entry fun sort_validators(_: &OperatorCap, staking: &mut Staking, validators: vector<ID>) {
        walstaking::assert_version(staking);
        walstaking::sort_validators(staking, validators);
    }

    /// Migrate the data version, this is called by the new package after upgrade.
    public entry fun migrate(_: &OperatorCap, staking: &mut Staking) {
        walstaking::migrate(staking);
    }

    public entry fun request_collect_rewards_fee(
        _: &OperatorCap, 
        system: &mut System,
        wal_staking: &mut WalStaking,
        staking: &mut Staking,
        ctx: &mut TxContext
    ) {
        abort 0
    }

    public entry fun claim_collect_rewards_fee(
        _: &OperatorCap, 
        system: &mut System, 
        wal_staking: &mut WalStaking,
        staking: &mut Staking, 
        account: address, 
        ctx: &mut TxContext
    ) {
        walstaking::assert_version(staking);
        walstaking::claim_collect_rewards_fee(system, wal_staking, staking, account, ctx);
    }

    public entry fun claim_collect_protocol_fee(
        _: &OperatorCap, 
        staking: &mut Staking, 
        account: address, 
        ctx: &mut TxContext
    ) {
        walstaking::assert_version(staking);
        walstaking::claim_collect_protocol_fee(staking, account, ctx);
    }

    public entry fun validator_offline(
        _: &OperatorCap,
        system: &System,
        wal_staking: &mut WalStaking,
        staking: &mut Staking,
        validator: ID,
        ctx: &mut TxContext
    ) {
        walstaking::assert_version(staking);
        walstaking::do_validator_offline(staking, system, wal_staking, validator, ctx);
    }
}
