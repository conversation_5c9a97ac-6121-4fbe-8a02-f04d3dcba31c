/// Interface for breaker.
module haedal::breaker {
    // use sui::tx_context::{TxContext};

    use haedal::manage::{BreakerCap};
    use haedal::walstaking::{Self, Staking};
    use haedal::config::{Self};
    use walrus::{
        staking::{Staking as WalStaking},
        system::{System}
    };

    public entry fun toggle_unstake_v2(_: &BreakerCap, staking: &mut Staking, status: bool) {
        walstaking::assert_version(staking);
        walstaking::toggle_unstake(staking, status);
    }

    public entry fun toggle_claim_v2(_: &BreakerCap, staking: &mut Staking, status: bool) {
        walstaking::assert_version(staking);
        walstaking::toggle_claim(staking, status);
    }
}
