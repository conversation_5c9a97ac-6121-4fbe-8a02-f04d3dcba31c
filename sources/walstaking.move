module haedal::walstaking {
    use sui::coin::{Self, Coin, TreasuryCap};
    use sui::balance::{Self, Balance};
    use sui::event;
    use sui::clock::{Self, Clock};
    use sui::table::{Self, Table};
    use sui::vec_map::{Self, VecMap};
    use sui::dynamic_field;
    use std::string::{utf8};
    use sui::{display, package};
    use wal::wal::WAL;
    use walrus::{
        staking::{Self, Staking as WalStaking},
        staked_wal::StakedWal,
        system::{System}
    };
    use haedal::config::{Self, StakingConfig};
    use haedal::hawal::{HAWAL};
    use haedal::vault::{Self, Vault};

    const MIN_STAKING_THRESHOLD: u64 = 1_000_000_000; // 1 WAL
    const DEFAULT_DEPOSIT_FEE_RATE: u64 = 0;
    const DEFAULT_REWARD_FEE_RATE: u64 = 60_0000;
    const DEFAULT_VALIDATOR_REWARD_FEE_RATE: u64 = 0;
    const DEFAULT_SERVICE_FEE_RATE: u64 = 90_0000;
    const DEFAULT_WALRUS_START_RPOCH: u32 = 1;
    const DEFAULT_WALRUS_START_TIME: u64 = 1742914804744;
    const DEFAULT_WALRUS_EPOCH_DURATION: u64 = 14 * 24 * 60 * 60 * 1000;
    const FEE_DENOMINATOR: u64 = 1000_0000;
    const EPOCH_FIRST_TWENTY_HOURS_MILI: u64 = 20*3600*1000;
    const DEFAULT_VALIDATOR_COUNT: u64 = 10;
    const EXCHANGE_RATE_PRECISION: u64 = 1_000_000;
    const PAUSE_CLAIM_KEY: vector<u8> = b"pause_claim_key";
    // Starting from 0, manually increment by 1 each time the program is upgraded.
    const PROGRAM_VERSION: u64 = 0;

    const EDataNotMatchProgram: u64 = 1;
    const EStakeNotEnoughWal: u64 = 2;
    const EStakeNoHaWalMint: u64 = 3;
    const EUnstakeNormalTicketLocking: u64 = 4;
    const EUnstakeExceedMaxWalAmount: u64 = 5;
    const EUnstakeNotZeroHawal: u64 = 6;
    const EStakePause: u64 = 7;
    const EUnstakePause: u64 = 8;
    const EUnstakeNeedAmountIsNotZero: u64 = 9;
    const EClaimPause: u64 = 10;
    const EValidatorCountNotMatch: u64 = 11;
    const EValidatorNotFound: u64 = 12;
    const EStakeActiveValidatorsNotFound: u64 = 13;
    const EUnstakeOutOfRange: u64 = 14;
    const EStakeActiveValidatorsIsNull: u64 = 15;
    const EUnstakeExceedMinWalAmount: u64 = 16;

    /// There is only one `Staking` share object.
    public struct Staking has key {
        id: UID,
        /// used to control the package upgrade
        version: u64,
        /// configuration for the protol
        config: StakingConfig,
        /// keep user's staked wal in current epoch, will be staked in validators at the end of currnet epoch
        wal_vault: Vault<WAL>,
        /// keep the protocol fee
        protocol_wal_vault: Vault<WAL>,
        /// TreasuryCap of the wrapped token
        hawal_treasury_cap: TreasuryCap<HAWAL>,
        /// total staked wal amount in history
        total_staked: u64,
        /// total unstaked wal amount in history
        total_unstaked: u64,
        /// total rewards in history
        total_rewards: u64,
        /// current unstaked but not yet claimed sui amount
        unclaimed_wal_amount:u64,
        /// total protocol fees in history
        collected_protocol_fees: u64,
        /// total protocol fees Pending in history
        collected_protocol_fees_pending: u64,
        /// uncollected protocol fees
        uncollected_protocol_fees: u64,
        /// the number value for haWAL supply, convenient for computing and querying
        hawal_supply: u64,
        pause_stake: bool,
        pause_unstake: bool,
        /// validators that have stakes, could be ordered by apy
        active_validators: vector<ID>,
        /// /// active validators in current epoch, updated every epoch.
        validators: vector<ID>,
        /// pools for validators
        pools: Table<ID, PoolInfo>,
        rewards_last_updated_epoch: u64
    }

    public struct PoolInfo has store {
        //staked_wals: TableQueue<StakedWal>,
        staked: VecMap<u32, StakedWal>, // Activation Epoch as key
        withdrawing: VecMap<u32, StakedWal>, // Withdrawing Epoch as key

        /// Total staked to this validator.
        total_staked: u64,
        /// The last updated rewards.
        rewards: u64,
    }

    /// The Unstake Normal ticket, an NFT held by issuer to claim the WAL back.
    public struct UnstakeTicket has key {
        id: UID,
        /// Timestamp unstake at
        unstake_timestamp_ms: u64,
        /// The original token amount
        hawal_amount: u64,
        /// The unstaked WAL amount
        wal_amount: u64,
        /// The claim epoch number
        claim_epoch: u32,
        /// Timestamp that the WAL can be claimed after
        claim_timestamp_ms: u64,
    }

    /// events
    public struct UserStaked has copy, drop {
        owner: address,
        wal_amount: u64,
        hawal_amount: u64,
        validator: ID,
    }

    public struct UserNormalUnstaked has copy, drop {
        //ticket_id: ID,
        owner: address,
        /// The unstake epoch number
        epoch: u32,
        /// Timestamp that the unstake epoch started at
        epoch_timestamp_ms: u64,
        /// Timestamp unstake at
        unstake_timestamp_ms: u64,
        wal_amount: u64,
        hawal_amount: u64,
    }

    public struct UserClaimed has copy, drop {
        /// UnstakeTicket ID
        id: ID, // ticket_id
        owner: address,
        wal_amount: u64,
    }

    public struct SystemStaked has copy, drop {
        staked_wal_id: ID,
        /// The stake epoch number
        epoch: u64,
        wal_amount: u64,
        validator: ID,
    }

    public struct WalRewardsUpdated has copy, drop {
        old: u64,
        new: u64,
        fee: u64,
    }

    public struct RequestRewardsFeeCollected has copy, drop {
        owner: address,
        epoch: u32,
        wal_amount: u64,
    }
    
    public struct RewardsFeeCollected has copy, drop {
        owner: address,
        wal_amount: u64,
    }

    public struct PoolSystemUnstaked has copy, drop {
        validator: ID,
        epoch: u32,
        wal_amount: u64,
    }

    public struct PoolSystemClaim has copy, drop {
        validator: ID,
        epoch: u32,
        wal_amount: u64,
    }

    public struct VersionUpdated has copy, drop {
        old: u64,
        new: u64,
    }

    public struct ExchangeRateUpdated has copy, drop {
        old: u64,
        new: u64,
    }

    public struct UserInstantUnstaked has copy, drop {
        owner: address,
        wal_amount: u64,
        hawal_amount: u64,
    }

    public struct UpdateValidatorOffline has copy, drop {
        validator: ID,
        new_validator:ID,
        total_staked: u64,
        total_withdrawing: u64,
    }

    /// our OTW to create display.
    public struct WALSTAKING has drop {}
    fun init(otw: WALSTAKING, ctx: &mut TxContext) {
        let publisher = package::claim(otw, ctx);
        let keys = vector[utf8(b"name"), utf8(b"image_url"), utf8(b"description")];

        let values = vector[
            // Let's add a demo name for our `DemoBear`
            utf8(b"Hawal-UnstakeTicket"),
            // Adding a happy bear image.
            utf8(
                b"https://assets.haedal.xyz/logos/hawal.svg",
            ),
            // Description is static for all bears out there.
            utf8(b"To Claim your WAL"),
        ];

        // Get a new `Display` object for the `Hero` type.
        let mut display = display::new_with_fields<UnstakeTicket>(
            &publisher,
            keys,
            values,
            ctx,
        );

        // Commit first version of `Display` to apply changes.
        display::update_version(&mut display);

        transfer::public_transfer(display, ctx.sender());
        transfer::public_transfer(publisher, ctx.sender())
    }

    /// call this after the module is deployed, and only can call it once
    public(package) fun initialize(cap: TreasuryCap<HAWAL>, ctx: &mut TxContext) {
        let sc = config::new(DEFAULT_DEPOSIT_FEE_RATE,
            DEFAULT_REWARD_FEE_RATE, DEFAULT_VALIDATOR_REWARD_FEE_RATE,
             DEFAULT_SERVICE_FEE_RATE, EPOCH_FIRST_TWENTY_HOURS_MILI, DEFAULT_VALIDATOR_COUNT,
             DEFAULT_WALRUS_START_RPOCH,DEFAULT_WALRUS_START_TIME,DEFAULT_WALRUS_EPOCH_DURATION
             );
        let staking_object = Staking {
            id: object::new(ctx),
            version: PROGRAM_VERSION, // used to control the data version, init start from PROGRAM_VERSION
            config: sc,
            wal_vault: vault::new<WAL>(ctx),
            protocol_wal_vault: vault::new<WAL>(ctx),
            hawal_treasury_cap: cap,
            total_staked: 0,
            total_unstaked: 0,
            total_rewards: 0,
            unclaimed_wal_amount: 0,
            collected_protocol_fees: 0,
            collected_protocol_fees_pending: 0,
            uncollected_protocol_fees: 0,
            hawal_supply: 0,
            pause_stake: false,
            pause_unstake: false,
            active_validators: vector::empty<ID>(),
            validators: vector::empty<ID>(),
            pools: table::new(ctx),
            rewards_last_updated_epoch: 0,
        };
        transfer::share_object<Staking>(staking_object);
    }

    /// stake and return haWAL
    public fun request_stake_coin(
        wal_staking: &mut WalStaking,
        staking: &mut Staking, 
        input: Coin<WAL>, 
        validator: ID, 
        ctx: &mut TxContext
    ): Coin<HAWAL> {
        assert_version(staking);
        assert!(!staking.pause_stake, EStakePause);
        // calculate the haWAL amount
        let input_wal_amount = coin::value(&input);
        assert!(input_wal_amount >= MIN_STAKING_THRESHOLD, EStakeNotEnoughWal);
        let active_validators_len = vector::length(&staking.active_validators);
        assert!(active_validators_len > 0, EStakeActiveValidatorsNotFound);
        let amount = get_hawal_by_wal(staking, input_wal_amount);
        assert!(amount > 0, EStakeNoHaWalMint);
        
        // mint haWAL and transfer to sender
        let sender = tx_context::sender(ctx);
        let ret = coin::mint(&mut staking.hawal_treasury_cap, amount, ctx);

        // user does not select a validator, or selects a invalid validator, just let protocol to select a validator
        if (validator == object::id_from_address(@0x0)) {
            // auto selects a validator
            let auto_validator = get_min_total_validator(staking);
            stake_to_validator(coin::into_balance(input), staking, wal_staking, auto_validator, ctx);
        } else { 
            // user selects a validator
            stake_to_validator(coin::into_balance(input), staking, wal_staking, validator, ctx);
        };

        // update the counters
        staking.total_staked = staking.total_staked + input_wal_amount;
        staking.hawal_supply = coin::total_supply(&staking.hawal_treasury_cap);

        event::emit(UserStaked {
            owner: sender,
            wal_amount: input_wal_amount,
            hawal_amount: amount,
            validator: validator,
        });

        ret
    }

    /// call the stake method to stake to validator
    fun stake_to_validator(
        bal: Balance<WAL>,
        staking: &mut Staking,
        wal_staking: &mut WalStaking, 
        validator: ID,
        ctx: &mut TxContext,
    ) {
        // search the validator's pool
        if (!table::contains(&staking.pools, validator)) {
            let pool = PoolInfo  {
                staked: vec_map::empty<u32, StakedWal>(),
                withdrawing: vec_map::empty<u32, StakedWal>(),
                total_staked: 0,
                rewards: 0,
            };
            table::add(&mut staking.pools, validator, pool);
            vector::push_back(&mut staking.validators, validator);
        };
        let pool = table::borrow_mut(&mut staking.pools, validator);

        let amount = balance::value(&bal);
        pool.total_staked = pool.total_staked + amount;
        let staked_wal = staking::stake_with_pool(wal_staking, coin::from_balance(bal, ctx), validator, ctx);
        // make sure each epoch has only 1 StakedWal
        let staked_wal_id = object::id(&staked_wal);
        let activation_epoch = staked_wal.activation_epoch();        
        if (pool.staked.contains(&activation_epoch)) {
            pool.staked.get_mut(&activation_epoch).join(staked_wal);
        } else {
            pool.staked.insert(activation_epoch, staked_wal);
        };

        event::emit(SystemStaked {
            staked_wal_id: staked_wal_id,
            epoch: tx_context::epoch(ctx),
            wal_amount: amount,
            validator: validator,
        });
    }

    /// request withdraw
    public fun request_withdraw_stake(
        system: &mut System, 
        wal_staking: &mut WalStaking, 
        staking: &mut Staking, 
        clock: &Clock, 
        input: Coin<HAWAL>, 
        ctx: &mut TxContext
    ) {
        assert_version(staking);
        assert!(!staking.pause_unstake, EUnstakePause);
        // calculate the wal amount
        let input_hawal_amount = coin::value(&input);
        assert!(input_hawal_amount > 0, EUnstakeNotZeroHawal);
        let max_exchange_wal_amount = get_wal_by_hawal(staking, input_hawal_amount);
        assert!(max_exchange_wal_amount >= MIN_STAKING_THRESHOLD, EUnstakeExceedMinWalAmount);
        assert!(max_exchange_wal_amount <=  get_total_wal(staking), EUnstakeExceedMaxWalAmount);
        // get walrus-epoch
        let walrus_current_epoch = system.epoch();
        let now_ms = clock::timestamp_ms(clock);
        let (current_epoch_start,mid_epoch_time,current_epoch_end,walrus_epoch_duration) = get_epoch_time_info(staking,walrus_current_epoch);
        assert!(now_ms >= current_epoch_start && now_ms <= current_epoch_end, EUnstakeOutOfRange);

        // n+1
        let mut claim_epoch = walrus_current_epoch + 1;
        let mut claim_timestamp_ms = current_epoch_start + walrus_epoch_duration;
        if (now_ms > mid_epoch_time) {
            // n+2
            claim_epoch = claim_epoch + 1;
            claim_timestamp_ms = claim_timestamp_ms + walrus_epoch_duration;
        };
        // create a ticket to request more wal, the sender need to claim after approved
        let ut = UnstakeTicket {
            id: object::new(ctx),
            unstake_timestamp_ms: now_ms,
            hawal_amount: input_hawal_amount,
            wal_amount: max_exchange_wal_amount,
            claim_epoch: claim_epoch,
            claim_timestamp_ms: claim_timestamp_ms,
        };
        let sender = tx_context::sender(ctx);
        transfer::transfer(ut, sender);

        // burn all the haWAL
        coin::burn(&mut staking.hawal_treasury_cap, input);
        event::emit(UserNormalUnstaked {
            owner: sender,
            epoch: claim_epoch,
            epoch_timestamp_ms: claim_timestamp_ms,
            unstake_timestamp_ms: now_ms,
            wal_amount: max_exchange_wal_amount,
            hawal_amount: input_hawal_amount,
        });

        // update the counters
        staking.total_unstaked = staking.total_unstaked + max_exchange_wal_amount;
        staking.hawal_supply = coin::total_supply(&staking.hawal_treasury_cap);
        staking.unclaimed_wal_amount = staking.unclaimed_wal_amount + max_exchange_wal_amount;

        let validators = staking.validators;
        let length = vector::length(&validators);
        let mut need_amount = max_exchange_wal_amount;
        let mut i = 0;
        while (i < length && need_amount > 0) {
            need_amount = do_validator_request_withdraw(staking, wal_staking, validators[i], need_amount, claim_epoch, ctx);
            i = i + 1;
        };

        assert!(need_amount == 0, EUnstakeNeedAmountIsNotZero);
    }

    /// unstake from validator and try to get need_amount WAL
    fun do_validator_request_withdraw(
        staking: &mut Staking,
        wal_staking: &mut WalStaking,
        validator: ID,
        mut need_amount: u64,
        current_epoch: u32,
        ctx: &mut TxContext,
    ): u64 {
        if (!table::contains(&staking.pools, validator)) {
            return need_amount
        };
        let original_need_amount = need_amount;
        let mut unstaked_amount = 0;
        let pool = table::borrow_mut(&mut staking.pools, validator);
        
        let keys = pool.staked.keys();
        let mut i = 0;
        let length = keys.length();
        while (need_amount > 0 && i < length) {
            let key = keys[i];
            // Limiting epoch
            if ( key <= current_epoch ){
                let staked_wal_ref = pool.staked.get_mut(&key);
                let (left_principal, withdraw_principal) = get_split_wal_amount(wal_staking, staked_wal_ref, need_amount, current_epoch);
                // decide whether withdraw entire or partial StakedWal
                let mut withdraw = if (left_principal > 0) {
                    let withdraw = staked_wal_ref.split(withdraw_principal, ctx);
                    withdraw
                } else {
                    let (_, withdraw) = pool.staked.remove(&key);
                    withdraw
                };
                let mut withdraw_rewards = 0;
                // can_withdraw_early
                if (staking::can_withdraw_staked_wal_early(wal_staking, &withdraw)){
                    // q1:n+2
                    // q1:n+1 and next_committee is null
                    // But there are no rewards
                    let withdrawed_wal = staking::withdraw_stake(wal_staking, withdraw, ctx);
                    vault::deposit(&mut staking.wal_vault, withdrawed_wal.into_balance());
                } else {
                    // q1:n+1 and node_in_next_committee
                    // q2:lte n OR gte n+3
                    // Have rewards
                    staking::request_withdraw_stake(wal_staking, &mut withdraw, ctx);
                    let withdraw_epoch = withdraw.withdraw_epoch();
                    withdraw_rewards = calculate_staked_wal_rewards(wal_staking, &withdraw, withdraw_epoch);
                    // new combined_key
                    let activation_epoch = withdraw.activation_epoch();
                    let combined_key = combine_epochs(withdraw_epoch, activation_epoch);
                    // old
                    if (pool.withdrawing.contains(&withdraw_epoch)) {
                        let existing_wal = pool.withdrawing.get_mut(&withdraw_epoch);
                        if (is_same_epochs(existing_wal, &withdraw)) {
                            existing_wal.join(withdraw);
                        } else {
                            if (pool.withdrawing.contains(&combined_key)) {
                                pool.withdrawing.get_mut(&combined_key).join(withdraw);
                            } else {
                                pool.withdrawing.insert(combined_key, withdraw);
                            };
                        };
                    } else {
                        //new combined_key
                        if (pool.withdrawing.contains(&combined_key)) {
                            pool.withdrawing.get_mut(&combined_key).join(withdraw);
                        } else {
                            pool.withdrawing.insert(combined_key, withdraw);
                        };
                    };
                };
                
                // withdraw and decrease the need_amount
                unstaked_amount = unstaked_amount + withdraw_principal + withdraw_rewards;
                if (withdraw_principal+withdraw_rewards <= need_amount) {
                    need_amount = need_amount - withdraw_principal - withdraw_rewards;
                } else {
                    need_amount = 0;
                };
            };
            i = i + 1;
        };

        // only emit event when there is any unstaking operation
        if (original_need_amount != need_amount) {
            event::emit(PoolSystemUnstaked {
                validator: validator,
                epoch: current_epoch,
                wal_amount: unstaked_amount,
            });
        };

        return need_amount
    }

    /// request_unstake_instant
    public fun request_unstake_instant(
        system: &System, 
        wal_staking: &mut WalStaking, 
        staking: &mut Staking, 
        clock: &Clock, 
        input: Coin<HAWAL>,
        ctx: &mut TxContext
    ): Coin<WAL> {
        assert_version(staking);
        assert!(!staking.pause_unstake, EUnstakePause);
        // get wal
        let input_hawal_amount = coin::value(&input);
        assert!(input_hawal_amount > 0, EUnstakeNotZeroHawal);
        let mut max_exchange_wal_amount = get_wal_by_hawal(staking, input_hawal_amount);
        assert!(max_exchange_wal_amount >= MIN_STAKING_THRESHOLD, EUnstakeExceedMinWalAmount);
        assert!(max_exchange_wal_amount <=  get_total_wal(staking), EUnstakeExceedMaxWalAmount);

        // loop pools get wal
        let (mut bal, need_amount) = do_unstake_instant(system, wal_staking, staking, max_exchange_wal_amount, ctx);
        assert!(need_amount == 0, EUnstakeNeedAmountIsNotZero);
        
        // check feee
        let service_fee = config::get_service_fee(&staking.config);
        let fee_amount = ((max_exchange_wal_amount as u128) * (service_fee as u128) / (FEE_DENOMINATOR as u128) as u64);
        if (fee_amount > 0){
            let fee_bal = bal.split(fee_amount);
            vault::deposit(&mut staking.protocol_wal_vault, fee_bal);    
        };
        // brun hawal
        coin::burn(&mut staking.hawal_treasury_cap, input);
        event::emit(UserInstantUnstaked {
            owner: tx_context::sender(ctx),
            wal_amount: max_exchange_wal_amount,
            hawal_amount: input_hawal_amount,
        });

        // update the counters
        staking.total_unstaked = staking.total_unstaked + max_exchange_wal_amount;
        staking.hawal_supply = coin::total_supply(&staking.hawal_treasury_cap);
        
        coin::from_balance(bal, ctx)
    }

    /// unstake from validator and try to get need_amount WAL
    fun do_unstake_instant(
        system: &System,
        wal_staking: &mut WalStaking,
        staking: &mut Staking,
        mut need_amount: u64,
        ctx: &mut TxContext,
    ): (Balance<WAL>,u64) {
        // loop pools get wal
        let current_epoch = system.epoch();
        let validators = staking.validators;
        let length = vector::length(&validators);
        let mut i = 0;
        let mut bal = balance::zero();
        while (i < length && need_amount > 0) {
            let validator = validators[i];
            i = i + 1;
            if (!table::contains(&staking.pools, validator)) {
                continue
            };
            let original_need_amount = need_amount;
            let mut unstaked_amount = 0;
            let pool = table::borrow_mut(&mut staking.pools, validator);
            
            let keys = pool.staked.keys();
            let mut k = 0;
            let lengthK = keys.length();
            while (k < lengthK) {
                let key = keys[k];
                let staked_wal_ref = pool.staked.get_mut(&key);
                if (staking::can_withdraw_staked_wal_early(wal_staking, staked_wal_ref)){
                    let (left_principal, withdraw_principal) = get_split_wal_amount(wal_staking, staked_wal_ref, need_amount, current_epoch);
                    // decide whether withdraw entire or partial StakedWal
                    let withdraw = if (left_principal > 0) {
                        let withdraw = staked_wal_ref.split(withdraw_principal, ctx);
                        withdraw
                    } else {
                        let (_, withdraw) = pool.staked.remove(&key);
                        withdraw
                    };
                    // withdraw_stake
                    let mut withdrawed_wal = staking::withdraw_stake(wal_staking, withdraw, ctx);
                    let withdrawed_value = withdrawed_wal.value();
                    unstaked_amount = unstaked_amount + withdrawed_value;
                    if (withdrawed_value <= need_amount) {
                        need_amount = need_amount - withdrawed_value;
                        balance::join(&mut bal, withdrawed_wal.into_balance());
                    } else {
                        let surplus_wal = withdrawed_wal.split(need_amount, ctx);
                        let surplus_wal_bal = surplus_wal.into_balance();
                        balance::join(&mut bal, surplus_wal_bal);
                        vault::deposit(&mut staking.wal_vault, withdrawed_wal.into_balance());
                        need_amount = 0;
                    };
                };
                k = k + 1;
            };

            // only emit event when there is any unstaking operation
            if (original_need_amount != need_amount) {
                event::emit(PoolSystemUnstaked {
                    validator: validator,
                    epoch: current_epoch,
                    wal_amount: unstaked_amount,
                });
            };
        };

        (bal,need_amount)
    }

    /// claim and return WAL
    public fun withdraw_stake(
        system: &mut System, 
        wal_staking: &mut WalStaking, 
        staking: &mut Staking, 
        clock: &Clock, 
        ticket: UnstakeTicket,
        ctx: &mut TxContext
    ): Coin<WAL> {
        assert_version(staking);
        assert!(!query_pause_claim(staking), EClaimPause);

        let UnstakeTicket { id, unstake_timestamp_ms:_, hawal_amount:_, wal_amount, claim_epoch, claim_timestamp_ms } = ticket;
        let key = object::uid_to_inner(&id);
        object::delete(id);

        let epoch = system.epoch();
        let now_ms = clock::timestamp_ms(clock);
        assert!(epoch>=claim_epoch, EUnstakeNormalTicketLocking);
        assert!(now_ms>=claim_timestamp_ms, EUnstakeNormalTicketLocking);

        // get WAL back from in order: wal_vault -> unstake from validators
        let bal = withdraw_wal_bal(system, wal_staking, staking, wal_amount, ctx);

        let sender = tx_context::sender(ctx);
        staking.unclaimed_wal_amount = staking.unclaimed_wal_amount - wal_amount;
        event::emit(UserClaimed {
            id: key,
            owner: sender,
            wal_amount,
        });

        coin::from_balance(bal, ctx)
    }

    /// withdraw firstly from staking.wal_vault, then unstake from validators
    fun withdraw_wal_bal(
        system: &System, 
        wal_staking: &mut WalStaking, 
        staking: &mut Staking, 
        mut need_amount :u64, 
        ctx: &mut TxContext
    ): Balance<WAL> {
        // before unstake from validators, make sure the rewards are updated for current epoch
        //update_total_rewards_onchain(staking, system, ctx); 

        let (mut bal, left_amount) = vault::withdraw_max(&mut staking.wal_vault, need_amount);
        need_amount = left_amount;

        if (need_amount > 0) {
            let mut unstaked_bal = balance::zero();
            let epoch = system.epoch();
            let validators = staking.validators;
            let length = vector::length(&validators);
            let mut i = 0;
            while (i < length && need_amount > 0) {
                let validator = *vector::borrow(&validators, i);
                need_amount = do_validator_withdraw(staking, wal_staking, &mut unstaked_bal, validator, need_amount, epoch, ctx);
                i = i + 1;
            };

            assert!(need_amount == 0, EUnstakeNeedAmountIsNotZero);

            bal.join(unstaked_bal.split(left_amount));
            staking.wal_vault.deposit(unstaked_bal);
        };

        bal
    }

    /// unstake from validator and try to get need_amount WAL
    fun do_validator_withdraw(
        staking: &mut Staking,
        wal_staking: &mut WalStaking,
        unstaked_bal: &mut Balance<WAL>,
        validator: ID,
        mut need_amount: u64,
        now_epoch: u32,
        ctx: &mut TxContext,
    ): u64 {
        if (!table::contains(&staking.pools, validator)) {
            return need_amount
        };
        let original_need_amount = need_amount;
        let mut unstaked_amount = 0;
        let pool = table::borrow_mut(&mut staking.pools, validator);
        let keys = pool.withdrawing.keys();
        let mut i = 0;
        let length = keys.length();
        while (need_amount > 0 && i < length) {
            let key = keys[i];
            let withdrawing_wal_ref = pool.withdrawing.get_mut(&key);
            let withdrawing_epoch = withdrawing_wal_ref.withdraw_epoch();
            if (withdrawing_wal_ref.is_withdrawing() && withdrawing_epoch <= now_epoch) {
                //split 
                let withdrawing_wal_ref_value = withdrawing_wal_ref.value();
                let withdraw = if (
                    need_amount > MIN_STAKING_THRESHOLD && 
                    need_amount + MIN_STAKING_THRESHOLD < withdrawing_wal_ref_value
                ) {
                    let withdraw = withdrawing_wal_ref.split(need_amount, ctx);
                    withdraw
                } else {
                    let (_, withdraw) = pool.withdrawing.remove(&key);
                    withdraw
                };
                // Since withdrawing_epoch is used in walrus to calculate revenue, withdrawing_epoch should also be used here
                let withdraw_principal = withdraw.value();
                let withdraw_rewards = calculate_staked_wal_rewards(wal_staking, &withdraw, withdrawing_epoch);
                
                let withdrawed_wal = staking::withdraw_stake(wal_staking, withdraw, ctx);
                let withdrawed_wal_bal = withdrawed_wal.into_balance();

                // update the pool
                pool.total_staked = pool.total_staked - withdraw_principal;
                // there is a case, if the pool's rewards are not updated for a long time, if we withdraw a lot of StakedSui, 
                // the withdraw_rewards may be greater than pool.rewards.
                if (pool.rewards >= withdraw_rewards) {
                    pool.rewards = pool.rewards - withdraw_rewards;
                } else {
                    pool.rewards = 0;
                };

                // withdraw and decrease the need_amount
                let withdrawed_wal_bal_amount = withdrawed_wal_bal.value();
                unstaked_bal.join(withdrawed_wal_bal);

                unstaked_amount = unstaked_amount + withdrawed_wal_bal_amount;
                if (withdrawed_wal_bal_amount <= need_amount) {
                    need_amount = need_amount - withdrawed_wal_bal_amount;
                } else {
                    need_amount = 0;
                    break
                };
            } else { // can not withdraw from now on
                break
            };

            i = i + 1;
        };

        // only emit event when there is any unstaking operation
        if (original_need_amount != need_amount) {
            event::emit(PoolSystemClaim {
                validator: validator,
                epoch: now_epoch,
                wal_amount: unstaked_amount,
            });
        };

        return need_amount
    }

    /// call this at the begining of every epoch, and it can be called several times in one epoch
    public(package) fun update_validator_rewards(
        staking: &mut Staking, 
        system: &System, 
        wal_staking: &WalStaking, 
        validator: ID, 
        ctx: &mut TxContext
    ) {
        if (!table::contains(&staking.pools, validator)) {
            return 
        };

        let mut total_rewards_increased = 0;
        let current_epoch = system.epoch();

        let pool = table::borrow_mut(&mut staking.pools, validator);
        total_rewards_increased = total_rewards_increased + calculate_validator_pool_rewards_increase(wal_staking, pool, current_epoch);

        // update the total rewards and protocol fees
        let old_exchange_rate = get_exchange_rate(staking);

        // calculate the protocol fee
        let protocol_fee = config::get_reward_fee(&staking.config);
        let protocol_fee_rewards = ((total_rewards_increased as u128) * (protocol_fee as u128) / (FEE_DENOMINATOR as u128) as u64);
        staking.uncollected_protocol_fees = staking.uncollected_protocol_fees + protocol_fee_rewards;

        // update the total_rewards, it affects the exchange rate
        let old_total_rewards = staking.total_rewards;
        staking.total_rewards = staking.total_rewards + total_rewards_increased;

        event::emit(WalRewardsUpdated{old: old_total_rewards, new: staking.total_rewards, fee: protocol_fee_rewards});

        event::emit(ExchangeRateUpdated{old: old_exchange_rate, new: get_exchange_rate(staking)});
    }

    /// Handle the node offline case: Immediately after the node is offline, 
    /// all pledges are removed and added to the active node
    public(package) fun do_validator_offline(
        staking: &mut Staking, 
        system: &System, 
        wal_staking: &mut WalStaking, 
        validator: ID, 
        ctx: &mut TxContext
    ) {
        if (!staking.pools.contains(validator)) {
            return 
        };
        // delete active_validators
        let (isInActive, iv) = staking.active_validators.index_of(&validator);
        if (isInActive){
            staking.active_validators.remove(iv);
        };
        
        // get new validator
        let auto_validator = get_min_total_validator(staking);
        let current_epoch = system.epoch();
        let pool = table::borrow_mut(&mut staking.pools, validator);

        // Remove all staked and repeat the stake
        let mut total_staked = balance::zero();
        let keysT = pool.staked.keys();
        let mut t = 0;
        let lengthT = keysT.length();
        while (t < lengthT) {
            let key = keysT[t];
            let (_, staked_ref) = pool.staked.remove(&key);
            let withdrawed_wal = staking::withdraw_stake(wal_staking, staked_ref, ctx);
            balance::join(&mut total_staked, withdrawed_wal.into_balance());
            t = t + 1;
        };

        // Withdraw all withdrawals and store them in wal_vault for subsequent use
        let mut total_withdrawing = 0;
        let keysW = pool.withdrawing.keys();
        let mut w = 0;
        let lengthW = keysW.length();
        while (w < lengthW) {
            let key = keysW[w];
            let (_,withdrawing_ref) = pool.withdrawing.remove(&key);
            let withdrawed_wal = staking::withdraw_stake(wal_staking, withdrawing_ref, ctx);
            total_withdrawing = total_withdrawing + withdrawed_wal.value();
            vault::deposit(&mut staking.wal_vault, withdrawed_wal.into_balance());
            w = w + 1;
        };
        
        // delete pool
        let (_, ivs) = staking.validators.index_of(&validator);
        staking.validators.remove(ivs);
        let PoolInfo {staked: staked, withdrawing: withdrawing, total_staked:_, rewards:_} = staking.pools.remove(validator);
        staked.destroy_empty();
        withdrawing.destroy_empty();

        // staked again
        let total_staked_vaule = total_staked.value();
        if(total_staked_vaule > 0){
            stake_to_validator(total_staked, staking, wal_staking, auto_validator, ctx);
        }else{
            total_staked.destroy_zero();
        };

        event::emit(UpdateValidatorOffline {
            validator: validator,
            new_validator: auto_validator, 
            total_staked: total_staked_vaule,
            total_withdrawing: total_withdrawing
        });
    }

    public(package) fun claim_collect_rewards_fee(
        system: &System, 
        wal_staking: &mut WalStaking,
        staking: &mut Staking,
        account: address, 
        ctx: &mut TxContext
    ) {
        /// wal_valut >> unstake_instant >> remainder
        let (mut bal,mut need_amount) = vault::withdraw_max(&mut staking.wal_vault, staking.uncollected_protocol_fees);
        let (bal_instant, need_amount) = do_unstake_instant(system, wal_staking, staking, need_amount, ctx);
        if(need_amount > 0){
            staking.uncollected_protocol_fees = need_amount;
        }else{
            staking.uncollected_protocol_fees = 0;
        };
        balance::join(&mut bal, bal_instant);

        let wal_amount = balance::value(&bal);
        transfer::public_transfer(coin::from_balance(bal, ctx), account);

        event::emit(RewardsFeeCollected {
            owner: account,
            wal_amount,
        });
    }

    public(package) fun claim_collect_protocol_fee(
        staking: &mut Staking,
        account: address, 
        ctx: &mut TxContext
    ) {
        let protocol_amount = vault::vault_amount(&staking.protocol_wal_vault);
        assert!(protocol_amount > 0);
        let bal = vault::withdraw_all(&mut staking.protocol_wal_vault);
        let wal_amount = balance::value(&bal);
        transfer::public_transfer(coin::from_balance(bal, ctx), account);

        event::emit(RewardsFeeCollected {
            owner: account,
            wal_amount,
        });
    }

    /// Sort the validators.
    public(package) fun sort_validators(staking: &mut Staking, validators: vector<ID>) {
        let length = vector::length(&staking.validators);
        assert!(vector::length(&validators) == length, EValidatorCountNotMatch);

        let mut i = 0;
        while (i < length ) {
            let validator = vector::borrow(&staking.validators, i);
            assert!(vector::contains(&validators, validator), EValidatorNotFound);
            i = i + 1;
        };

        staking.validators = validators;
    }

    public(package) fun set_active_validators(staking: &mut Staking, active_validators: vector<ID>) {
        staking.active_validators = active_validators;
    }

    public(package) fun migrate(staking: &mut Staking) {
        assert!(staking.version < PROGRAM_VERSION, EDataNotMatchProgram);
        event::emit(VersionUpdated{old: staking.version, new: PROGRAM_VERSION});
        staking.version = PROGRAM_VERSION;
    }

    public fun assert_version(staking: &Staking) {
        assert!(staking.version == PROGRAM_VERSION, EDataNotMatchProgram);
    }

    /// return walrus epoch [start_time,mid_time,end_time]
    fun get_epoch_time_info(staking: &mut Staking, current_epoch:u32):(u64,u64,u64,u64){
        assert!(current_epoch > 0, EUnstakeNeedAmountIsNotZero);
        let config = get_config_mut(staking);
        let walrus_start_epoch = config::get_walrus_start_epoch(config);
        let walrus_start_timestamp_ms = config::get_walrus_start_timestamp_ms(config);
        let walrus_epoch_duration = config::get_walrus_epoch_duration(config);

        assert!(walrus_start_timestamp_ms > 0 && walrus_epoch_duration > 0, EUnstakeNeedAmountIsNotZero);
        assert!(current_epoch >= walrus_start_epoch, EUnstakeNeedAmountIsNotZero);
        let epoch_diff = (current_epoch - walrus_start_epoch) as u64;

        let current_epoch_start = walrus_start_timestamp_ms + (epoch_diff * walrus_epoch_duration);
        let current_epoch_end = current_epoch_start + walrus_epoch_duration;
        let mid_epoch_time = current_epoch_start + (walrus_epoch_duration / 2);
        
        (current_epoch_start,mid_epoch_time,current_epoch_end,walrus_epoch_duration)
    }

    /// calculate all the StakedWal rewards, and store in the PoolInfo.rewards.
    fun calculate_validator_pool_rewards_increase(
        wal_staking: &WalStaking, 
        pool: &mut PoolInfo, 
        current_epoch: u32
    ): u64 {
        let mut pool_rewards = 0;
        let keys = pool.staked.keys();
        let mut i = 0;
        let length = keys.length();
        while (i < length) {
            let key = keys[i];
            let staked_wal_ref = pool.staked.get(&key);
            pool_rewards = pool_rewards + calculate_staked_wal_rewards(wal_staking, staked_wal_ref, current_epoch);
            i = i + 1;
        };

        let keys = pool.withdrawing.keys();
        let mut i = 0;
        let length = keys.length();
        while (i < length) {
            let key = keys[i];
            let withdrawing_wal_ref = pool.withdrawing.get(&key);
            let withdrawing_epoch = if(withdrawing_wal_ref.withdraw_epoch() < current_epoch){
                withdrawing_wal_ref.withdraw_epoch()
            }else{
                current_epoch
            };
            pool_rewards = pool_rewards + calculate_staked_wal_rewards(wal_staking, withdrawing_wal_ref, withdrawing_epoch);
            i = i + 1;
        };

        // update pool rewards
        // possibly there are some splits of StakedWals, which caused the rewards precision loss
        // we just keep the pool.rewards unchanged firstly, and only update when it increases.
        let mut pool_rewards_increased = 0;
        if (pool_rewards > pool.rewards) {
            pool_rewards_increased = pool_rewards - pool.rewards;
            pool.rewards = pool_rewards;
        };
        return pool_rewards_increased
    }

    fun calculate_staked_wal_rewards(wal_staking: &WalStaking, staked_wal_ref: &StakedWal, current_epoch: u32): u64 {
        staking::calculate_rewards(
            wal_staking,
            staked_wal_ref.node_id(),
            staked_wal_ref.value(),
            staked_wal_ref.activation_epoch(),
            current_epoch,
        )
    }

    // check withdraw_epoch activation_epoch
    fun is_same_epochs(staked_wal1: &StakedWal, staked_wal2: &StakedWal): bool {
        let withdraw_epoch1 = staked_wal1.withdraw_epoch();
        let activation_epoch1 = staked_wal1.activation_epoch();
        let withdraw_epoch2 = staked_wal2.withdraw_epoch();
        let activation_epoch2 = staked_wal2.activation_epoch();
        
        (withdraw_epoch1 == withdraw_epoch2) && (activation_epoch1 == activation_epoch2)
    }
    // get key
    fun combine_epochs(withdraw_epoch: u32, activation_epoch: u32): u32 {
        // max u16
        let max_u16 = std::u16::max_value!() as u32;
        // %
        let activation_bits = activation_epoch & max_u16;
        let withdraw_bits = withdraw_epoch & max_u16;

        (withdraw_bits << 16) | activation_bits
    }

    fun get_min_total_validator(staking: &Staking): ID {
        let active_validators = staking.active_validators;
        let length = vector::length(&active_validators);
        assert!(length > 0, EStakeActiveValidatorsIsNull);

        let mut i = 0;
        let mut min_id = active_validators[0];
        let mut has_non_pool_validator = false;
        // Take the nodes that have not yet pledged
        while (i < length) {
            let validator = active_validators[i];
            if (!table::contains(&staking.pools, validator)) {
                min_id = validator;
                has_non_pool_validator = true;
                break
            };
            i = i + 1;
        };

        // If all validators are in the staking.pools, find the one with the smallest total_staked
        if (!has_non_pool_validator) {
            let mut p = 1;
            let pool_frist = table::borrow(&staking.pools, active_validators[0]);
            let mut min_total: u64 = pool_frist.total_staked;
            min_id = active_validators[0];
            while (p < length) {
                let validator = active_validators[p];
                let pool = table::borrow(&staking.pools, validator);
                if (pool.total_staked < min_total) {
                    min_id = validator;
                    min_total = pool.total_staked;
                };

                p = p + 1;
            };
        };

        return min_id
    }

    /// return (left_principal, withdraw_principal)
    fun get_split_wal_amount(
        wal_staking: &WalStaking, 
        staked_wal_ref: &StakedWal, 
        need_amount: u64, 
        current_epoch: u32
    ): (u64, u64) {
        // calculate total value of the staked_wal
        let principal = staked_wal_ref.value();
        let rewards = calculate_staked_wal_rewards(wal_staking, staked_wal_ref, current_epoch);
        let staked_wal_with_rewards = principal + rewards;
        // withdraw entire or partial of the statked_wal
        let (left_principal, withdraw_principal) = if (staked_wal_with_rewards <= need_amount) { // withdraw entire StakedWal
            (0, principal)
        } else { // split StakedWal and only withdraw partial
            let mut withdraw_principal = mul_div(need_amount, principal, staked_wal_with_rewards) + 1;
            let left_principal = if (principal >= withdraw_principal) {
                principal - withdraw_principal
            } else {
                withdraw_principal = principal;
                0
            };

            if (withdraw_principal >= MIN_STAKING_THRESHOLD) {
                if (left_principal >= MIN_STAKING_THRESHOLD) { // can be split
                    (left_principal, withdraw_principal)
                } else { // can't be split
                    (0, principal)
                }
            } else {
                if (principal >= 2*MIN_STAKING_THRESHOLD) { // can be split
                    (principal-MIN_STAKING_THRESHOLD, MIN_STAKING_THRESHOLD) // withdraw at least 1 SUI
                } else { // can't be split
                    (0, principal)
                }
            }
       };

       (left_principal, withdraw_principal)
    }

    public fun get_hawal_by_wal(staking: &Staking, wal_amount: u64): u64 {
        let total_wal_amount_snapshot = get_total_wal(staking);
        if (total_wal_amount_snapshot == 0 || staking.hawal_supply == 0) {
            return wal_amount
        };

        let res = (staking.hawal_supply as u128)
                * (wal_amount as u128)
                / (total_wal_amount_snapshot as u128);
        (res as u64)
    }

    public fun get_wal_by_hawal(staking: &Staking, hawal_amount: u64): u64 {
        let total_wal_amount_snapshot = get_total_wal(staking);
        if (total_wal_amount_snapshot == 0 || staking.hawal_supply == 0) {
            return hawal_amount
        };

        let res = (total_wal_amount_snapshot as u128)
                * (hawal_amount as u128)
                / (staking.hawal_supply as u128);
        (res as u64)
    }

    /// wal -> haWAL , used off-chain
    public fun get_exchange_rate(staking: &Staking): u64 {
        let total_wal_amount_snapshot = get_total_wal(staking);
        if (total_wal_amount_snapshot == 0 || staking.hawal_supply == 0) {
            return EXCHANGE_RATE_PRECISION
        };

        ((total_wal_amount_snapshot as u128) * (EXCHANGE_RATE_PRECISION as u128) / (staking.hawal_supply as u128) as u64)
    }

    /// total_instant_unstake
    public fun get_total_instant_unstake_wal(
        system: &System, 
        wal_staking: &WalStaking, 
        staking: &Staking
    ): u64 {
        let mut total_instant = 0;
        let current_epoch = system.epoch();
        let validators = staking.validators;
        let length = vector::length(&validators);
        let mut i = 0;
        while (i < length) {
            let validator = validators[i];
            i = i + 1;
            if (!table::contains(&staking.pools, validator)) {
                continue
            };
            let pool = table::borrow(&staking.pools, validator);
            let keys = pool.staked.keys();
            let mut k = 0;
            let lengthK = keys.length();
            while (k < lengthK) {
                let key = keys[k];
                let staked_wal_ref = pool.staked.get(&key);

                // can_withdraw_early
                if (staking::can_withdraw_staked_wal_early(wal_staking, staked_wal_ref)){
                    total_instant = total_instant + staked_wal_ref.value();
                };
                k = k + 1;
            };  
        };
        total_instant
    }

    /// the total wal
    public fun get_total_wal(staking: &Staking): u64 {
        staking.total_staked + staking.total_rewards - staking.collected_protocol_fees - staking.uncollected_protocol_fees - staking.total_unstaked
    }

    /// the total wal cap
    public fun get_total_wal_cap(staking: &Staking): u64 {
        get_total_wal(staking) + staking.unclaimed_wal_amount
    }
    public fun get_version(staking: &Staking): u64 {
        staking.version
    }
    public fun get_config_mut(staking: &mut Staking): &mut StakingConfig {
        assert_version(staking);
        &mut staking.config
    }
    public fun get_total_staked(staking: &Staking): u64 {
        staking.total_staked
    }
    public fun get_total_unstaked(staking: &Staking): u64 {
        staking.total_unstaked
    }
    public fun get_total_rewards(staking: &Staking): u64 {
        staking.total_rewards
    }
    public fun get_hawal_supply(staking: &Staking): u64 {
        staking.hawal_supply
    }
    public fun get_wal_vault_amount(staking: &Staking): u64 {
        vault::vault_amount(&staking.wal_vault)
    }
    public fun get_protocol_wal_vault_amount(staking: &Staking): u64 {
        vault::vault_amount(&staking.protocol_wal_vault)
    }
    public fun get_collected_protocol_fees(staking: &Staking): u64 {
        staking.collected_protocol_fees
    }
    public fun get_uncollected_protocol_fees(staking: &Staking): u64 {
        staking.uncollected_protocol_fees
    }
    public fun get_unclaimed_wal_amount(staking: &Staking): u64 {
        staking.unclaimed_wal_amount
    }

    public(package) fun toggle_stake(staking: &mut Staking, status: bool) {
        staking.pause_stake = status;
    }

    public(package) fun toggle_unstake(staking: &mut Staking, status: bool) {
        staking.pause_unstake = status;
    }

    public(package) fun toggle_claim(staking: &mut Staking, status: bool) {
        if (!dynamic_field::exists_with_type<vector<u8>, bool>(&staking.id, PAUSE_CLAIM_KEY)) {
            dynamic_field::add(&mut staking.id, PAUSE_CLAIM_KEY, status);
        } else {
            *dynamic_field::borrow_mut(&mut staking.id, PAUSE_CLAIM_KEY) = status;
        }
    }

    public fun query_pause_claim(staking: &Staking): bool {
        if (dynamic_field::exists_with_type<vector<u8>, bool>(&staking.id, PAUSE_CLAIM_KEY)) {
            return *dynamic_field::borrow(&staking.id, PAUSE_CLAIM_KEY)
        } else {
            return false
        }
    }

    /// query used by offchain
    public fun get_staked_validators(staking: &Staking): vector<ID> {
        staking.validators
    }

    public fun get_staked_pools(staking: &mut Staking, validator: ID): &mut PoolInfo {
        assert!(table::contains(&staking.pools, validator));
        table::borrow_mut(&mut staking.pools, validator)
    }

    public fun get_staked_validator(staking: &Staking, validator: ID): bool {
        vector::contains(&staking.validators, &validator)
    }

    public fun get_epoch_time_ms_and_epoch(system:&mut System,staking: &mut Staking):(u64,u64,u64,u64,u32){
        let walrus_current_epoch = system.epoch();
        let (current_epoch_start,mid_epoch_time,current_epoch_end,walrus_epoch_duration) = get_epoch_time_info(staking,walrus_current_epoch);

        (current_epoch_start,mid_epoch_time,current_epoch_end,walrus_epoch_duration,walrus_current_epoch)
    }

    /// getters for UnstakeTicket
    public fun ticket_unstake_timestamp_ms(ticket: &UnstakeTicket): u64 {
        ticket.unstake_timestamp_ms
    }
    public fun ticket_hawal_amount(ticket: &UnstakeTicket): u64 {
        ticket.hawal_amount
    }
    public fun ticket_wal_amount(ticket: &UnstakeTicket): u64 {
        ticket.wal_amount
    }
    public fun ticket_claim_epoch(ticket: &UnstakeTicket): u32 {
        ticket.claim_epoch
    }
    public fun ticket_claim_timestamp_ms(ticket: &UnstakeTicket): u64 {
        ticket.claim_timestamp_ms
    }

    fun mul_div(x: u64, y: u64, z: u64): u64 {
        ((x as u128) * (y as u128) / (z as u128) as u64)
    }
}
