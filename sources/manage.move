/// Interface for admin.
module haedal::manage {
    use sui::coin::{TreasuryCap};
    use haedal::hawal::{HAWAL};
    use haedal::walstaking::{Self,Staking};
    use walrus::{
        staking::{Staking as WalStaking},
        system::{System}
    };
    use haedal::config;

    const EInitialized: u64 = 1;

    /// `AdminCap` is used by an administrator.
    public struct AdminCap has store, key {
        id: UID,
        init: bool,
    }

    /// `OperatorCap` is used by the offchain programs.
    public struct OperatorCap has store, key {
        id: UID,
    }

    // `MinorSignCap` is used by n of 2
    public struct MinorSignCap has store, key {
        id: UID,
    }

    // `BreakerCap` is used by the circuit breaker.
    public struct BreakerCap has store, key {
        id: UID,
    }
    
    // `RobotCap` is used by the robot.
    public struct RobotCap has key, store {
        id: UID,
    }

    fun init(ctx: &mut TxContext) {
        let sender = tx_context::sender(ctx);

        transfer::transfer<AdminCap>(AdminCap {
            id: object::new(ctx),
            init: false,
        }, sender);        
    }

    #[test_only]
    public fun init_staking_for_test(ctx: &mut TxContext) {
        init(ctx);
    }

    /// for admins
    public entry fun initialize(cap: &mut AdminCap, treasuryCap: TreasuryCap<HAWAL>, ctx: &mut TxContext) {
        assert!(!cap.init, EInitialized);

        walstaking::initialize(treasuryCap, ctx);
        cap.init = true;
    }

    public entry fun set_operator_cap_to_address(_: &AdminCap, account: address, ctx: &mut TxContext) {
        transfer::transfer<OperatorCap>(OperatorCap {
            id: object::new(ctx),
        }, account);
    }

    public entry fun set_minor_sign_cap_to_address(_: &AdminCap, account: address, ctx: &mut TxContext) {
        transfer::transfer<MinorSignCap>(MinorSignCap {
            id: object::new(ctx),
        }, account);
    }

    public entry fun set_breaker_cap_to_address(_: &AdminCap, account: address, ctx: &mut TxContext) {
        transfer::transfer<BreakerCap>(BreakerCap {
            id: object::new(ctx),
        }, account);
    }
    
    public entry fun set_robot_cap_to_address(_: &AdminCap, account: address, ctx: &mut TxContext) {
        transfer::transfer<RobotCap>(RobotCap {
            id: object::new(ctx),
        }, account);
    }

    /// Migrate the data version, this is called by the new package after upgrade.
    public entry fun migrate(_: &AdminCap, staking: &mut Staking) {
        walstaking::migrate(staking);
    }

    public entry fun request_collect_rewards_fee(
        _: &AdminCap, 
        system: &mut System,
        wal_staking: &mut WalStaking,
        staking: &mut Staking,
        ctx: &mut TxContext
    ) {
        abort 0
    }

    public entry fun claim_collect_rewards_fee(
        _: &AdminCap, 
        system: &mut System, 
        wal_staking: &mut WalStaking,
        staking: &mut Staking, 
        account: address, 
        ctx: &mut TxContext
    ) {
        walstaking::assert_version(staking);
        walstaking::claim_collect_rewards_fee(system, wal_staking, staking, account, ctx);
    }

    public entry fun set_deposit_fee_v2(_: &AdminCap, staking: &mut Staking, deposit_fee: u64) {
        walstaking::assert_version(staking);
        config::set_deposit_fee(walstaking::get_config_mut(staking), deposit_fee);
    }

    public entry fun set_reward_fee_v2(_: &AdminCap, staking: &mut Staking, reward_fee: u64) {
        walstaking::assert_version(staking);
        config::set_reward_fee(walstaking::get_config_mut(staking), reward_fee);
    }

    public entry fun set_validator_reward_fee_v2(_: &AdminCap, staking: &mut Staking, validator_reward_fee: u64) {
        walstaking::assert_version(staking);
        config::set_validator_reward_fee(walstaking::get_config_mut(staking), validator_reward_fee);
    }

    public entry fun set_service_fee_v2(_: &AdminCap, staking: &mut Staking, service_fee: u64) {
        walstaking::assert_version(staking);
        config::set_service_fee(walstaking::get_config_mut(staking), service_fee);
    }

    public entry fun claim_collect_rewards_fee_v2(
        _: &AdminCap, 
        system: &mut System, 
        wal_staking: &mut WalStaking,
        staking: &mut Staking, 
        account: address, 
        ctx: &mut TxContext
    ) {
        walstaking::assert_version(staking);
        walstaking::claim_collect_rewards_fee(system, wal_staking, staking, account, ctx);
    }

    public entry fun claim_collect_protocol_fee_v2(
        _: &AdminCap, 
        staking: &mut Staking, 
        account: address, 
        ctx: &mut TxContext
    ) {
        walstaking::assert_version(staking);
        walstaking::claim_collect_protocol_fee(staking, account, ctx);
    }
}
