import { getFullnodeUrl, SuiClient } from '@mysten/sui/client';
import fs from 'fs';
import { appendFile } from 'fs/promises';
import * as dotenv from 'dotenv';
dotenv.config();

const filename = 'node/output/walrus_committee_info.json';
const filename1 = 'node/output/walrus_next_committee_info.json';


async function appendToFile(filename, content) {
  try {
    await appendFile(filename, content + '\n', 'utf8');
    console.log('Add file success:', filename);
  } catch (error) {
    console.error('Add file error:', error);
  }
}

const client = new SuiClient({ url: getFullnodeUrl('testnet') });


//get curt_commission pool
const committeeInfoobj = await client.call('sui_getObject', 
	[
		process.env.WalrusStakingID,
		{
			"showContent": true,
		}
	]
);
const commissionPoolsId = committeeInfoobj.data.content.fields.value.fields.committee.fields.pos0.fields.contents.map(res => ({
	[res.fields.key]: (res.fields.value.length)/1000
  }));

const commissionPoolsMap = Object.assign({}, ...commissionPoolsId);

//next_commission
const next_committee = committeeInfoobj.data.content.fields.value.fields.next_committee.fields.pos0.fields.contents.map((res)=>{return res.fields.key});
console.log(next_committee,'next_committee')
appendToFile(filename1, JSON.stringify(next_committee, null, 2));

//all pools
let pools = [];
let nextCursor = "";
while(true){
	let committeeInfo;
	if(nextCursor){
		committeeInfo = await client.call('suix_getDynamicFields', 
			[
				process.env.WalrusStakingInnerID,
				nextCursor,
				100
			]
		);
	}else{
		committeeInfo = await client.call('suix_getDynamicFields', 
			[
				process.env.WalrusStakingInnerID,
			]
		);
	}
	// console.log(committeeInfo.data[0])
	pools = [...pools,...committeeInfo.data];
	if(committeeInfo.hasNextPage){
		nextCursor = committeeInfo.nextCursor;
	}else{
		nextCursor = "";
		break;
	}
}

// get pool info
console.log(pools.length);
for(const pool of pools){
	console.log(pool.objectId,'object_id');
	let obj_id= pool.objectId;
	const committeeInfoobj1 = await client.call('sui_getObject', 
		[
			obj_id,
			{
				"showType": false,
				"showOwner": false,
				"showPreviousTransaction": false,
				"showDisplay": false,
				"showContent": true,
				"showBcs": false,
				"showStorageRebate": false
			}
		]
	);
	
	const fileds = committeeInfoobj1.data.content.fields;
	const node_id = fileds.node_info.fields.node_id;
	const node_name = fileds.node_info.fields.name;
	const commission_rate = fileds.commission_rate;
	const total_stake = fileds.wal_balance;
	
	let metadata_id = fileds.node_info.fields.metadata.fields.id.id;
	const committeeInfo2 = await client.call('suix_getDynamicFields', 
		[
			metadata_id,
		]
	);
	
	let obj2_id  = committeeInfo2.data[0].objectId;
	const committeeInfoobj2 = await client.call('sui_getObject', 
		[
			obj2_id,
			{
				"showType": false,
				"showOwner": false,
				"showPreviousTransaction": false,
				"showDisplay": false,
				"showContent": true,
				"showBcs": false,
				"showStorageRebate": false
			}
		]
	);

	const fields2 = committeeInfoobj2.data.content.fields;
	const image_url = fields2.value.fields.image_url;
	const description = fields2.value.fields.description;
	console.log(node_id,'nodeid');
	const is_commission = commissionPoolsMap[node_id] > 0 ?true:false;
	const rewards_pool = commissionPoolsMap[node_id] ?? 0 ;

	const datas = {node_id,node_name,image_url,description,rewards_pool,commission_rate,total_stake,is_commission}
	
	appendToFile(filename, JSON.stringify(datas, null, 2)+",");
}