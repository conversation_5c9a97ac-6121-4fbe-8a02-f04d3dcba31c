lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@mysten/bcs':
        specifier: ^1.5.0
        version: 1.5.0
      '@mysten/sui':
        specifier: ^1.21.2
        version: 1.21.2(typescript@5.7.3)
      '@mysten/sui.js':
        specifier: ^0.54.1
        version: 0.54.1(typescript@5.7.3)
      '@ton/core':
        specifier: ^0.60.1
        version: 0.60.1(@ton/crypto@3.3.0)
      '@ton/crypto':
        specifier: ^3.3.0
        version: 3.3.0
      '@ton/ton':
        specifier: ^15.2.1
        version: 15.2.1(@ton/core@0.60.1(@ton/crypto@3.3.0))(@ton/crypto@3.3.0)
      buffer:
        specifier: ^6.0.3
        version: 6.0.3
      dotenv:
        specifier: ^16.4.7
        version: 16.4.7

packages:

  '@0no-co/graphql.web@1.1.1':
    resolution: {integrity: sha512-F2i3xdycesw78QCOBHmpTn7eaD2iNXGwB2gkfwxcOfBbeauYpr8RBSyJOkDrFtKtVRMclg8Sg3n1ip0ACyUuag==}
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0
    peerDependenciesMeta:
      graphql:
        optional: true

  '@0no-co/graphqlsp@1.12.16':
    resolution: {integrity: sha512-B5pyYVH93Etv7xjT6IfB7QtMBdaaC07yjbhN6v8H7KgFStMkPvi+oWYBTibMFRMY89qwc9H8YixXg8SXDVgYWw==}
    peerDependencies:
      graphql: ^15.5.0 || ^16.0.0 || ^17.0.0
      typescript: ^5.0.0

  '@gql.tada/cli-utils@1.6.3':
    resolution: {integrity: sha512-jFFSY8OxYeBxdKi58UzeMXG1tdm4FVjXa8WHIi66Gzu9JWtCE6mqom3a8xkmSw+mVaybFW5EN2WXf1WztJVNyQ==}
    peerDependencies:
      '@0no-co/graphqlsp': ^1.12.13
      '@gql.tada/svelte-support': 1.0.1
      '@gql.tada/vue-support': 1.0.1
      graphql: ^15.5.0 || ^16.0.0 || ^17.0.0
      typescript: ^5.0.0
    peerDependenciesMeta:
      '@gql.tada/svelte-support':
        optional: true
      '@gql.tada/vue-support':
        optional: true

  '@gql.tada/internal@1.0.8':
    resolution: {integrity: sha512-XYdxJhtHC5WtZfdDqtKjcQ4d7R1s0d1rnlSs3OcBEUbYiPoJJfZU7tWsVXuv047Z6msvmr4ompJ7eLSK5Km57g==}
    peerDependencies:
      graphql: ^15.5.0 || ^16.0.0 || ^17.0.0
      typescript: ^5.0.0

  '@graphql-typed-document-node/core@3.2.0':
    resolution: {integrity: sha512-mB9oAsNCm9aM3/SOv4YtBMqZbYj10R7dkq8byBqxGY/ncFwhf2oQzMV+LCRlWoDSEBJ3COiR1yeDvMtsoOsuFQ==}
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0

  '@mysten/bcs@0.11.1':
    resolution: {integrity: sha512-xP85isNSYUCHd3O/g+TmZYmg4wK6cU8q/n/MebkIGP4CYVJZz2wU/G24xIZ3wI+0iTop4dfgA5kYrg/DQKCUzA==}

  '@mysten/bcs@1.4.0':
    resolution: {integrity: sha512-YwDYspceLt8b7v6ohPvy8flQEi+smtfSG5d2A98CbUA48XBmOqTSPNmpw9wsZVVnrH2avr+BS5uVhDZT+EquYA==}

  '@mysten/bcs@1.5.0':
    resolution: {integrity: sha512-v39dm5oNfKYMAf2CVI+L0OaJiG9RVXsjqPM4BwTKcHNCZOvr35IIewGtXtWXsI67SQU2TRq8lhQzeibdiC/CNg==}

  '@mysten/sui.js@0.54.1':
    resolution: {integrity: sha512-TSmGIX7U9O/uS9EKIQdv7/S69KTbBhMJVelXCafJE6IJw8iB9cN9uLu0+uklkBSDrbRmLSEY70jMr3uRFjReIg==}
    engines: {node: '>=16'}
    deprecated: This package has been renamed to @mysten/sui, please update to use the renamed package.

  '@mysten/sui@1.21.2':
    resolution: {integrity: sha512-8AesvczokAUv796XiOo8af2+1IYA9bRon11Ra+rwehvqhz+sMRT8A+Cw5sDnlSc9/aQwM51JQKUnvMczNbpfYA==}
    engines: {node: '>=18'}

  '@noble/curves@1.8.1':
    resolution: {integrity: sha512-warwspo+UYUPep0Q+vtdVB4Ugn8GGQj8iyB3gnRWsztmUHTI3S1nhdiWNsPUGL0vud7JlRRk1XEu7Lq1KGTnMQ==}
    engines: {node: ^14.21.3 || >=16}

  '@noble/hashes@1.7.1':
    resolution: {integrity: sha512-B8XBPsn4vT/KJAGqDzbwztd+6Yte3P4V7iafm24bxgDe/mlRuK6xmWPuCNrKt2vDafZ8MfJLlchDG/vYafQEjQ==}
    engines: {node: ^14.21.3 || >=16}

  '@scure/base@1.2.4':
    resolution: {integrity: sha512-5Yy9czTO47mqz+/J8GM6GIId4umdCk1wc1q8rKERQulIoc8VP9pzDcghv10Tl2E7R96ZUx/PhND3ESYUQX8NuQ==}

  '@scure/bip32@1.6.2':
    resolution: {integrity: sha512-t96EPDMbtGgtb7onKKqxRLfE5g05k7uHnHRM2xdE6BP/ZmxaLtPek4J4KfVn/90IQNrU1IOAqMgiDtUdtbe3nw==}

  '@scure/bip39@1.5.4':
    resolution: {integrity: sha512-TFM4ni0vKvCfBpohoh+/lY05i9gRbSwXWngAsF4CABQxoaOHijxuaZ2R6cStDQ5CHtHO9aGJTr4ksVJASRRyMA==}

  '@suchipi/femver@1.0.0':
    resolution: {integrity: sha512-bprE8+K5V+DPX7q2e2K57ImqNBdfGHDIWaGI5xHxZoxbKOuQZn4wzPiUxOAHnsUr3w3xHrWXwN7gnG/iIuEMIg==}

  '@ton/core@0.60.1':
    resolution: {integrity: sha512-8FwybYbfkk57C3l9gvnlRhRBHbLYmeu0LbB1z9N+dhDz0Z+FJW8w0TJlks8CgHrAFxsT3FlR2LsqFnsauMp38w==}
    peerDependencies:
      '@ton/crypto': '>=3.2.0'

  '@ton/crypto-primitives@2.1.0':
    resolution: {integrity: sha512-PQesoyPgqyI6vzYtCXw4/ZzevePc4VGcJtFwf08v10OevVJHVfW238KBdpj1kEDQkxWLeuNHEpTECNFKnP6tow==}

  '@ton/crypto@3.3.0':
    resolution: {integrity: sha512-/A6CYGgA/H36OZ9BbTaGerKtzWp50rg67ZCH2oIjV1NcrBaCK9Z343M+CxedvM7Haf3f/Ee9EhxyeTp0GKMUpA==}

  '@ton/ton@15.2.1':
    resolution: {integrity: sha512-ICzozzATRfymkVfFVZrfVpKnCc5PLxAVeaB62mx/HsgllsjnR64UuoLuE6hqWHcA3/Hft9YLGdk2/rOHGZM6qA==}
    peerDependencies:
      '@ton/core': '>=0.60.0'
      '@ton/crypto': '>=3.2.0'

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  axios@1.8.3:
    resolution: {integrity: sha512-iP4DebzoNlP/YN2dpwCgb8zoCmhtkajzS48JvwmkSkXvPI3DHc7m+XYL5tGnSlJtR6nImXZmdCuN5aP8dh1d8A==}

  base-x@4.0.1:
    resolution: {integrity: sha512-uAZ8x6r6S3aUM9rbHGVOIsR15U/ZSc82b3ymnCPsT45Gk1DDvhDPdIgB5MrhirZWt+5K0EEPQH985kNqZgNPFw==}

  base-x@5.0.0:
    resolution: {integrity: sha512-sMW3VGSX1QWVFA6l8U62MLKz29rRfpTlYdCqLdpLo1/Yd4zZwSbnUaDfciIAowAqvq7YFnWq9hrhdg1KYgc1lQ==}

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  bech32@2.0.0:
    resolution: {integrity: sha512-LcknSilhIGatDAsY1ak2I8VtGaHNhgMSYVxFrGLXv+xLHytaKZKcaUJJUE7qmBr7h33o5YQwP55pMI0xmkpJwg==}

  bs58@5.0.0:
    resolution: {integrity: sha512-r+ihvQJvahgYT50JD05dyJNKlmmSlMoOGwn1lCcEzanPglg7TxYjioQUYehQ9mAR/+hOSd2jRc/Z2y5UxBymvQ==}

  bs58@6.0.0:
    resolution: {integrity: sha512-PD0wEnEYg6ijszw/u8s+iI3H17cTymlrwkKhDhPZq+Sokl3AU4htyBFTjAeNAlCCmg0f53g6ih3jATyCKftTfw==}

  buffer@6.0.3:
    resolution: {integrity: sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  dataloader@2.2.3:
    resolution: {integrity: sha512-y2krtASINtPFS1rSDjacrFgn1dcUuoREVabwlOGOe4SdxenREqwjwjElAdwvbGM7kgZz9a3KVicWR7vcz8rnzA==}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  dotenv@16.4.7:
    resolution: {integrity: sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ==}
    engines: {node: '>=12'}

  dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}

  es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==}
    engines: {node: '>= 0.4'}

  follow-redirects@1.15.9:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  form-data@4.0.2:
    resolution: {integrity: sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==}
    engines: {node: '>= 6'}

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}

  get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}

  gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  gql.tada@1.8.10:
    resolution: {integrity: sha512-FrvSxgz838FYVPgZHGOSgbpOjhR+yq44rCzww3oOPJYi0OvBJjAgCiP6LEokZIYND2fUTXzQAyLgcvgw1yNP5A==}
    hasBin: true
    peerDependencies:
      typescript: ^5.0.0

  graphql@16.10.0:
    resolution: {integrity: sha512-AjqGKbDGUFRKIRCP9tCKiIGHyriz2oHEbPIbEtcSLSs4YjReZOIPQQWek4+6hjw62H9QShXHyaGivGiYVLeYFQ==}
    engines: {node: ^12.22.0 || ^14.16.0 || ^16.0.0 || >=17.0.0}

  has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  jose@5.10.0:
    resolution: {integrity: sha512-s+3Al/p9g32Iq+oqXxkW//7jk2Vig6FF1CFqzVXoTUXt2qz89YWbL+OwS17NFYEvxC35n0FKeGO2LGYSxeM2Gg==}

  jssha@3.2.0:
    resolution: {integrity: sha512-QuruyBENDWdN4tZwJbQq7/eAK85FqrI4oDbXjy5IBhYD+2pTJyBUWZe8ctWaCkrV0gy6AaelgOZZBMeswEa/6Q==}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  poseidon-lite@0.2.1:
    resolution: {integrity: sha512-xIr+G6HeYfOhCuswdqcFpSX47SPhm0EpisWJ6h7fHlWwaVIvH3dLnejpatrtw6Xc6HaLrpq05y7VRfvDmDGIog==}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  superstruct@1.0.4:
    resolution: {integrity: sha512-7JpaAoX2NGyoFlI9NBh66BQXGONc+uE+MRS5i2iOBKuS4e+ccgMDjATgZldkah+33DakBxDHiss9kvUcGAO8UQ==}
    engines: {node: '>=14.0.0'}

  symbol.inspect@1.0.1:
    resolution: {integrity: sha512-YQSL4duoHmLhsTD1Pw8RW6TZ5MaTX5rXJnqacJottr2P2LZBF/Yvrc3ku4NUpMOm8aM0KOCqM+UAkMA5HWQCzQ==}

  teslabot@1.5.0:
    resolution: {integrity: sha512-e2MmELhCgrgZEGo7PQu/6bmYG36IDH+YrBI1iGm6jovXkeDIGa3pZ2WSqRjzkuw2vt1EqfkZoV5GpXgqL8QJVg==}

  tweetnacl@1.0.3:
    resolution: {integrity: sha512-6rt+RN7aOi1nGMyC4Xa5DdYiukl2UWCbcJft7YhxReBGQD7OAM8Pbxw6YMo4r2diNEA8FEmu32YOn9rhaiE5yw==}

  typescript@5.7.3:
    resolution: {integrity: sha512-84MVSjMEHP+FQRPy3pX9sTVV/INIex71s9TL2Gm5FG/WG1SqXeKyZ0k7/blY/4FdOzI12CBy1vGc4og/eus0fw==}
    engines: {node: '>=14.17'}
    hasBin: true

  valibot@0.36.0:
    resolution: {integrity: sha512-CjF1XN4sUce8sBK9TixrDqFM7RwNkuXdJu174/AwmQUB62QbCQADg5lLe8ldBalFgtj1uKj+pKwDJiNo4Mn+eQ==}

  zod@3.24.2:
    resolution: {integrity: sha512-lY7CDW43ECgW9u1TcT3IoXHflywfVqDYze4waEz812jR/bZ8FHDsl7pFQoSZTz5N+2NqRXs8GBwnAwo3ZNxqhQ==}

snapshots:

  '@0no-co/graphql.web@1.1.1(graphql@16.10.0)':
    optionalDependencies:
      graphql: 16.10.0

  '@0no-co/graphqlsp@1.12.16(graphql@16.10.0)(typescript@5.7.3)':
    dependencies:
      '@gql.tada/internal': 1.0.8(graphql@16.10.0)(typescript@5.7.3)
      graphql: 16.10.0
      typescript: 5.7.3

  '@gql.tada/cli-utils@1.6.3(@0no-co/graphqlsp@1.12.16(graphql@16.10.0)(typescript@5.7.3))(graphql@16.10.0)(typescript@5.7.3)':
    dependencies:
      '@0no-co/graphqlsp': 1.12.16(graphql@16.10.0)(typescript@5.7.3)
      '@gql.tada/internal': 1.0.8(graphql@16.10.0)(typescript@5.7.3)
      graphql: 16.10.0
      typescript: 5.7.3

  '@gql.tada/internal@1.0.8(graphql@16.10.0)(typescript@5.7.3)':
    dependencies:
      '@0no-co/graphql.web': 1.1.1(graphql@16.10.0)
      graphql: 16.10.0
      typescript: 5.7.3

  '@graphql-typed-document-node/core@3.2.0(graphql@16.10.0)':
    dependencies:
      graphql: 16.10.0

  '@mysten/bcs@0.11.1':
    dependencies:
      bs58: 5.0.0

  '@mysten/bcs@1.4.0':
    dependencies:
      bs58: 6.0.0

  '@mysten/bcs@1.5.0':
    dependencies:
      '@scure/base': 1.2.4

  '@mysten/sui.js@0.54.1(typescript@5.7.3)':
    dependencies:
      '@graphql-typed-document-node/core': 3.2.0(graphql@16.10.0)
      '@mysten/bcs': 0.11.1
      '@noble/curves': 1.8.1
      '@noble/hashes': 1.7.1
      '@scure/bip32': 1.6.2
      '@scure/bip39': 1.5.4
      '@suchipi/femver': 1.0.0
      bech32: 2.0.0
      gql.tada: 1.8.10(graphql@16.10.0)(typescript@5.7.3)
      graphql: 16.10.0
      superstruct: 1.0.4
      tweetnacl: 1.0.3
    transitivePeerDependencies:
      - '@gql.tada/svelte-support'
      - '@gql.tada/vue-support'
      - typescript

  '@mysten/sui@1.21.2(typescript@5.7.3)':
    dependencies:
      '@graphql-typed-document-node/core': 3.2.0(graphql@16.10.0)
      '@mysten/bcs': 1.4.0
      '@noble/curves': 1.8.1
      '@noble/hashes': 1.7.1
      '@scure/bip32': 1.6.2
      '@scure/bip39': 1.5.4
      '@suchipi/femver': 1.0.0
      bech32: 2.0.0
      gql.tada: 1.8.10(graphql@16.10.0)(typescript@5.7.3)
      graphql: 16.10.0
      jose: 5.10.0
      poseidon-lite: 0.2.1
      valibot: 0.36.0
    transitivePeerDependencies:
      - '@gql.tada/svelte-support'
      - '@gql.tada/vue-support'
      - typescript

  '@noble/curves@1.8.1':
    dependencies:
      '@noble/hashes': 1.7.1

  '@noble/hashes@1.7.1': {}

  '@scure/base@1.2.4': {}

  '@scure/bip32@1.6.2':
    dependencies:
      '@noble/curves': 1.8.1
      '@noble/hashes': 1.7.1
      '@scure/base': 1.2.4

  '@scure/bip39@1.5.4':
    dependencies:
      '@noble/hashes': 1.7.1
      '@scure/base': 1.2.4

  '@suchipi/femver@1.0.0': {}

  '@ton/core@0.60.1(@ton/crypto@3.3.0)':
    dependencies:
      '@ton/crypto': 3.3.0
      symbol.inspect: 1.0.1

  '@ton/crypto-primitives@2.1.0':
    dependencies:
      jssha: 3.2.0

  '@ton/crypto@3.3.0':
    dependencies:
      '@ton/crypto-primitives': 2.1.0
      jssha: 3.2.0
      tweetnacl: 1.0.3

  '@ton/ton@15.2.1(@ton/core@0.60.1(@ton/crypto@3.3.0))(@ton/crypto@3.3.0)':
    dependencies:
      '@ton/core': 0.60.1(@ton/crypto@3.3.0)
      '@ton/crypto': 3.3.0
      axios: 1.8.3
      dataloader: 2.2.3
      symbol.inspect: 1.0.1
      teslabot: 1.5.0
      zod: 3.24.2
    transitivePeerDependencies:
      - debug

  asynckit@0.4.0: {}

  axios@1.8.3:
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.2
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  base-x@4.0.1: {}

  base-x@5.0.0: {}

  base64-js@1.5.1: {}

  bech32@2.0.0: {}

  bs58@5.0.0:
    dependencies:
      base-x: 4.0.1

  bs58@6.0.0:
    dependencies:
      base-x: 5.0.0

  buffer@6.0.3:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  dataloader@2.2.3: {}

  delayed-stream@1.0.0: {}

  dotenv@16.4.7: {}

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  follow-redirects@1.15.9: {}

  form-data@4.0.2:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      mime-types: 2.1.35

  function-bind@1.1.2: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  gopd@1.2.0: {}

  gql.tada@1.8.10(graphql@16.10.0)(typescript@5.7.3):
    dependencies:
      '@0no-co/graphql.web': 1.1.1(graphql@16.10.0)
      '@0no-co/graphqlsp': 1.12.16(graphql@16.10.0)(typescript@5.7.3)
      '@gql.tada/cli-utils': 1.6.3(@0no-co/graphqlsp@1.12.16(graphql@16.10.0)(typescript@5.7.3))(graphql@16.10.0)(typescript@5.7.3)
      '@gql.tada/internal': 1.0.8(graphql@16.10.0)(typescript@5.7.3)
      typescript: 5.7.3
    transitivePeerDependencies:
      - '@gql.tada/svelte-support'
      - '@gql.tada/vue-support'
      - graphql

  graphql@16.10.0: {}

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  ieee754@1.2.1: {}

  jose@5.10.0: {}

  jssha@3.2.0: {}

  math-intrinsics@1.1.0: {}

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  poseidon-lite@0.2.1: {}

  proxy-from-env@1.1.0: {}

  superstruct@1.0.4: {}

  symbol.inspect@1.0.1: {}

  teslabot@1.5.0: {}

  tweetnacl@1.0.3: {}

  typescript@5.7.3: {}

  valibot@0.36.0: {}

  zod@3.24.2: {}
