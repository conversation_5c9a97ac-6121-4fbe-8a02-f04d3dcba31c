import { getFullnodeUrl, SuiClient } from '@mysten/sui/client';
import { appendFile } from 'fs/promises';
import * as dotenv from 'dotenv';
dotenv.config();


const filename = 'output/hawal_pools_info.json';
async function appendToFile(filename, content) {
  try {
    await appendFile(filename, content + '\n', 'utf8');
    console.log('Add file success:', filename);
  } catch (error) {
    console.error('Add file error:', error);
  }
}

const client = new SuiClient({ url: getFullnodeUrl('mainnet') });

let pools = [];
let nextCursor = "";
while(true){
	let committeeInfo;
	if(nextCursor){
		committeeInfo = await client.call('suix_getDynamicFields', 
			[
				process.env.HawalPoolID,
				nextCursor,
				100
			]
		);
	}else{
		committeeInfo = await client.call('suix_getDynamicFields', 
			[
				process.env.HawalPoolID,
			]
		);
	}
	console.log(committeeInfo.data,"committeeInfo.data")
	pools = [...pools,...committeeInfo.data];
	if(committeeInfo.hasNextPage){
		nextCursor = committeeInfo.nextCursor;
	}else{
		nextCursor = "";
		break;
	}
}

//
console.log(pools.length);
for(const pool of pools){
	console.log(pool.objectId,'object_id');
	let obj_id= pool.objectId;
	const committeeInfoobj1 = await client.call('sui_getObject', 
		[
			obj_id,
			{
				"showType": false,
				"showOwner": false,
				"showPreviousTransaction": false,
				"showDisplay": false,
				"showContent": true,
				"showBcs": false,
				"showStorageRebate": false
			}
		]
	);
	
	const fileds = committeeInfoobj1.data.content.fields;
	// console.log(fileds.value,'data');
	appendToFile(filename, JSON.stringify(fileds.value, null, 2)+",");
}
