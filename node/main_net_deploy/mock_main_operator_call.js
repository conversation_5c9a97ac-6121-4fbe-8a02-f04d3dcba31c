import { getFullnodeUrl, SuiClient } from '@mysten/sui/client';
import { getFaucetHost, requestSuiFromFaucetV1 } from '@mysten/sui/faucet';
import { MIST_PER_SUI } from '@mysten/sui/utils';
import { Transaction } from '@mysten/sui/transactions';
import { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519';
import * as dotenv from 'dotenv';
dotenv.config();


const keypair = Ed25519Keypair.fromSecretKey(process.env.KeyPair);
const address = keypair.getPublicKey().toSuiAddress();

const client = new SuiClient({ url: getFullnodeUrl('mainnet') });

// node init.js
const operator_cap = process.env.OperatorCap;
const us_staking = process.env.HawalStaking;
const package_id = process.env.HawalPackageId;
const wal_staking = process.env.WalrusStakingID;
const wal_system = process.env.WalrusSystemID;



// await update_validator_rewards()

await claim_collect_protocol_fee(address);

// toggle
async function toggle_stake(toggle) {
	let tx1 = new Transaction();
	tx1.moveCall({
		target: package_id+'::operate::toggle_stake',
		arguments: [
			tx1.object(operator_cap), 
			tx1.object(us_staking), 
			tx1.pure.bool(toggle)
		],
	});
	const res_set_walrus_epoch_start = await client.signAndExecuteTransaction({
		transaction: tx1,
		signer: keypair,
	});
	console.log(res_set_walrus_epoch_start,'res_set_walrus_epoch_start');
}

// up
async function update_validator_rewards() {
	const validators = [
		"0x01ef8dad1ea8afe318fe2003a5123b20c4fe6511659ecf46d968851c15b67134",
		"0x03094763089d274035f83e75eed4822fd98140eb4bc48e05d5fc93d5f3514d79",
		"0x052e285b24cdbd1bd37d33aa38978383a5d78f4d3ab1bfe20e9923ce4e674d87",
		"0x0b99da03c851df39f57a88478f1335d0429174f93bd177b1731dde6cd7e9210d",
		"0x12b6a397a9d039f6a8c442ba15e317dcbc371f47d2cfeaa0a9678e870cf32520",
		"0x15f4b6a3127e92c79d6bf267bb3fac0bb9ff8af5bcdae2690afb64090f91f095",
		"0x164ebc90922f768dcc076edc022107c5f22d41273eb1de4ef0fd6339d26e6aa0",
		"0x14e714e69d212c910848498fff99083b0fa8b10e6e833c77635a5697cda82560",
		"0x001e1696af97d14ceefdf5771c0ca74f5375d9b0ae57ccdb2b9d8ce212762b6e",
		"0x015e3daef941d0d87de2c586bebacbde4c8a29c60f09d8e8eb854c5719c002d1"
	]

	for(let val of validators){
		let tx4 = new Transaction();
		tx4.moveCall({
			target: package_id+'::operate::update_validator_rewards',
			arguments: [
				tx4.object(operator_cap),
				tx4.object(us_staking),
				tx4.object(wal_system),
				tx4.object(wal_staking),
				tx4.pure.id(val)
			],
		});
	
		const result_update_validator_rewards = await client.signAndExecuteTransaction({
			transaction: tx4,
			signer: keypair,
		});
	
		console.log(result_update_validator_rewards,'result_update_validator_rewards');
		await sleep(2000);
	}
	
}

// srot nodes
async function sort_validators() {
	const validators = [
		"0x03094763089d274035f83e75eed4822fd98140eb4bc48e05d5fc93d5f3514d79",
		"0x052e285b24cdbd1bd37d33aa38978383a5d78f4d3ab1bfe20e9923ce4e674d87",
		"0x0b99da03c851df39f57a88478f1335d0429174f93bd177b1731dde6cd7e9210d",
		"0x12b6a397a9d039f6a8c442ba15e317dcbc371f47d2cfeaa0a9678e870cf32520",
		"0x15f4b6a3127e92c79d6bf267bb3fac0bb9ff8af5bcdae2690afb64090f91f095",
		"0x164ebc90922f768dcc076edc022107c5f22d41273eb1de4ef0fd6339d26e6aa0",
		"0x14e714e69d212c910848498fff99083b0fa8b10e6e833c77635a5697cda82560",
		"0x015e3daef941d0d87de2c586bebacbde4c8a29c60f09d8e8eb854c5719c002d1",
		"0x001e1696af97d14ceefdf5771c0ca74f5375d9b0ae57ccdb2b9d8ce212762b6e",
		"0x01ef8dad1ea8afe318fe2003a5123b20c4fe6511659ecf46d968851c15b67134",
	]

	let tx4 = new Transaction();
	tx4.moveCall({
		target: package_id+'::operate::sort_validators',
		arguments: [
			tx4.object(operator_cap),
			tx4.object(us_staking),
			tx4.pure.vector("id",validators)
		],
	});

	const result_update_validator_rewards = await client.signAndExecuteTransaction({
		transaction: tx4,
		signer: keypair,
	});

	console.log(result_update_validator_rewards,'result_update_validator_rewards');
}

// cliam
async function claim_collect_protocol_fee(address) {
	let tx4 = new Transaction();
	tx4.moveCall({
		target: package_id+'::operate::claim_collect_protocol_fee',
		arguments: [
			tx4.object(operator_cap),
			tx4.object(us_staking),
			tx4.pure.address(address)
		],
	});

	const result_claim_collect_protocol_fee = await client.signAndExecuteTransaction({
		transaction: tx4,
		signer: keypair,
	});

	console.log(result_claim_collect_protocol_fee,'result_claim_collect_protocol_fee');
}
function sleep(ms) {
	return new Promise(resolve => setTimeout(resolve, ms));
}
