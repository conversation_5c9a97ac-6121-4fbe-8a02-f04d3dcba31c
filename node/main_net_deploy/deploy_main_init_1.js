import { getFullnodeUrl, SuiClient } from '@mysten/sui/client';
import { getFaucetHost, requestSuiFromFaucetV1 } from '@mysten/sui/faucet';
import { MIST_PER_SUI } from '@mysten/sui/utils';
import { Transaction } from '@mysten/sui/transactions';
import { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519';
import * as dotenv from 'dotenv';
dotenv.config();

const keypair = Ed25519Keypair.fromSecretKey(process.env.KeyPair);
const address = keypair.getPublicKey().toSuiAddress();
console.log(address,'address');
const client = new SuiClient({ url: getFullnodeUrl('mainnet') });

const package_id = process.env.HawalPackageId;
const admin_cap = process.env.AdminCap;
const treasuryCap = process.env.TreasuryCap;
const upCap = process.env.upCap;

// hawal-staking admin_cap
const tx = new Transaction();
tx.moveCall({
	target: package_id+'::manage::initialize',
	arguments: [
        tx.object(admin_cap), 
        tx.object(treasuryCap),
    ],
});
const res_init = await client.signAndExecuteTransaction({
	transaction: tx,
	signer: keypair,
});
console.log(res_init,'initialize');

const tx1 = new Transaction();
tx1.moveCall({
	target: package_id+'::manage::set_operator_cap_to_address',
	arguments: [
        tx1.object(admin_cap),
        tx1.pure.address(process.env.OperatorAddress),
    ],
});
const res_init1 = await client.signAndExecuteTransaction({
	transaction: tx1,
	signer: keypair,
});
console.log(res_init1,'set_operator_cap_to_address');