import { getFullnodeUrl, SuiClient } from '@mysten/sui/client';
import { getFaucetHost, requestSuiFromFaucetV1 } from '@mysten/sui/faucet';
import { MIST_PER_SUI } from '@mysten/sui/utils';
import { Transaction } from '@mysten/sui/transactions';
import { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519';
import * as dotenv from 'dotenv';
dotenv.config();

const keypair = Ed25519Keypair.fromSecretKey(process.env.KeyPair);
const address = keypair.getPublicKey().toSuiAddress();

console.log(address,'address');
const client = new SuiClient({ url: getFullnodeUrl('mainnet') });

const package_id = process.env.HawalPackageId;
const us_staking = process.env.HawalStaking;
// stakeing - nodeID
const wal_staking = process.env.WalrusStakingID;
const coin_wal = "0xe2d288ff592779155ac5fae8cf6d266bdd2ccc37f1506a212b7afb0782070f07";
// let tx2 = new Transaction();
// tx2.moveCall({
// 	target: package_id+'::interface::request_stake',
// 	arguments: [
// 		tx2.object(wal_staking),
//         tx2.object(us_staking),
//         tx2.object(coin_wal),
//         tx2.pure('id','0x03b1a2e1823d0cbca5707ecc9bda0abbf24f9a2631a3943fd84471dc34c2c50d')//node_id
//     ],
// });

// const result_request_stake = await client.signAndExecuteTransaction({
// 	transaction: tx2,
// 	signer: keypair,
// });
// console.log(result_request_stake,'result_request_stake');

const wal_system = process.env.WalrusSystemID;
const coin_hawal = "0xbafd2d19bd6eebeeecb5b390bbcb5675ebb4888168efb130b9947f8d30bbeff2";
// request_unstake_instant
let txin = new Transaction();
txin.moveCall({
	target: package_id+'::interface::request_unstake_instant',
	arguments: [
		txin.object(wal_system),
		txin.object(wal_staking),
		txin.object(us_staking),
		txin.object("0x6"),
		txin.object(coin_hawal),
	],
});

const result_request_unstake_instant = await client.signAndExecuteTransaction({
	transaction: txin,
	signer: keypair,
});
console.log(result_request_unstake_instant,'result_request_unstake_instant');

// //request_unstake_delay
// let tx3 = new Transaction();
// tx3.moveCall({
// 	target: package_id+'::interface::request_unstake_delay',
// 	arguments: [
// 		tx3.object(wal_system),
// 		tx3.object(wal_staking),
//         tx3.object(us_staking),
// 		tx3.object("0x6"),
//         tx3.object(coin_hawal),
//     ],
// });

// const result_request_unstake_delay = await client.signAndExecuteTransaction({
// 	transaction: tx3,
// 	signer: keypair,
// });
// console.log(result_request_unstake_delay,'result_request_unstake_delay');


// const unstakeTicket = "";
// let tx4 = new Transaction();
// tx4.moveCall({
// 	target: package_id+'::interface::claim',
// 	arguments: [
// 		tx4.object(wal_system),
// 		tx4.object(wal_staking),
//         tx4.object(us_staking),
// 		tx3.object("0x6"),
// 		tx3.object(unstakeTicket),
//     ],
// });

// const result_claim = await client.signAndExecuteTransaction({
// 	transaction: tx4,
// 	signer: keypair,
// });
// console.log(result_claim,'result_claim');
