import { getFullnodeUrl, SuiClient } from '@mysten/sui/client';
import { getFaucetHost, requestSuiFromFaucetV1 } from '@mysten/sui/faucet';
import { MIST_PER_SUI } from '@mysten/sui/utils';
import { Transaction } from '@mysten/sui/transactions';
import { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519';
import * as dotenv from 'dotenv';
dotenv.config();

const client = new SuiClient({ url: getFullnodeUrl('mainnet') });
const keypair = Ed25519Keypair.fromSecretKey(process.env.KeyPair);
const package_id = process.env.HawalPackageId;
const operator_cap = process.env.OperatorCap;
const us_staking = process.env.HawalStaking;

let tx = new Transaction();
// nodes_ids
// const nodes = [
// 	"0x001e1696af97d14ceefdf5771c0ca74f5375d9b0ae57ccdb2b9d8ce212762b6e",
// 	"0x015e3daef941d0d87de2c586bebacbde4c8a29c60f09d8e8eb854c5719c002d1",
// ];
const nodes = process.env.Nodes;
tx.moveCall({
	target: package_id+'::operate::set_active_validators',
	arguments: [
        tx.object(operator_cap), 
        tx.object(us_staking),
		tx.pure.vector("id",JSON.parse(nodes)),
    ],
});
const res_set_active_validators = await client.signAndExecuteTransaction({
	transaction: tx,
	signer: keypair,
});
console.log(res_set_active_validators,'res_set_active_validators');



// walrus defult epoch 1  1742914804744 1209600000

// let tx1 = new Transaction();
// tx1.moveCall({
// 	target: package_id+'::operate::set_walrus_epoch_start',
// 	arguments: [
//         tx1.object(operator_cap), 
// 		tx1.object(us_staking), 
// 		tx1.pure.u32(1),
// 		tx1.pure.u64(1742914804744),
// 		tx1.pure.u64(1209600000)
//     ],
// });
// const res_set_walrus_epoch_start = await client.signAndExecuteTransaction({
// 	transaction: tx1,
// 	signer: keypair,
// });
// console.log(res_set_walrus_epoch_start,'res_set_walrus_epoch_start');
