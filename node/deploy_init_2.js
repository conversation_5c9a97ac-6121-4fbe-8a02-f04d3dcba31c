import { getFullnodeUrl, SuiClient } from '@mysten/sui/client';
import { getFaucetHost, requestSuiFromFaucetV1 } from '@mysten/sui/faucet';
import { MIST_PER_SUI } from '@mysten/sui/utils';
import { Transaction } from '@mysten/sui/transactions';
import { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519';
import * as dotenv from 'dotenv';
dotenv.config();

const keypair = Ed25519Keypair.fromSecretKey(process.env.KeyPair);
const address = keypair.getPublicKey().toSuiAddress();
console.log(address,'address');
const client = new SuiClient({ url: getFullnodeUrl('mainnet') });

// node init.js
const package_id = process.env.HawalPackageId;
const us_staking = process.env.HawalStaking;

// for init1 result
const operator_cap = process.env.OperatorCap;

let tx = new Transaction();
// nodes_ids
const nodes = [
	"0xb07ab3db6b190fe6e32e499e7c79499786174689ae835485c178da0e9a977180",
	"0xf11fef95c8c5a17c2cbc51c15483e38585cf996110b8d50b8e1957442dc736fd",
	"0x82ff00c685e4946c9c2fc45e031af8eb3188ea74e9dafa494ee2bf50032a5851",
	"0x61d5598a35e198e6cafac7eb808191da3742a2a1789716ab89a68a1f934ee5c6",
	"0x555f07999a9b95af4750c530e2a77c4733055975438ce627857ee75134320b3c",
	"0x7c09670cbf67f4a9213177364c6b70bac7922d21affd0ea88450270c43d3587a",
	"0x23bd98e815ce5d18aa3f0c33c26528b235a80ab07520df1e65801346635f41f5",
	"0x136efbe7c65217b1eaa9eda246709be90baaf871562437921963439e21619d61",
	"0x5cd6d5ec90f82f366c6a95825da71466ae61c407a0340b949fb5b2e6ad636d68",
	"0x4eff55b548c313eeea2c5dc9d564f2f0afe7ea2d61deaa93abfc814d3882d234",
];
// const nodes =  process.env.Nodes;
tx.moveCall({
	target: package_id+'::operate::set_active_validators',
	arguments: [
        tx.object(operator_cap), 
        tx.object(us_staking),
		tx.pure.vector("id",nodes),
    ],
});
const res_set_active_validators = await client.signAndExecuteTransaction({
	transaction: tx,
	signer: keypair,
});
console.log(res_set_active_validators,'res_set_active_validators');


// mock config for walrus
// epoch 1 1737217990337 172800000
// let tx1 = new Transaction();
// tx1.moveCall({
// 	target: package_id+'::operate::set_walrus_epoch_start',
// 	arguments: [
//         tx1.object(operator_cap), 
// 		tx1.object(us_staking), 
// 		tx1.pure.u32(1),
// 		tx1.pure.u64(1737217990337),
// 		tx1.pure.u64(172800000)
//     ],
// });
// const res_set_walrus_epoch_start = await client.signAndExecuteTransaction({
// 	transaction: tx1,
// 	signer: keypair,
// });
// console.log(res_set_walrus_epoch_start,'res_set_walrus_epoch_start');
