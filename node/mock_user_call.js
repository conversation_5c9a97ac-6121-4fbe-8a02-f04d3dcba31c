import { getFullnodeUrl, SuiClient } from '@mysten/sui/client';
import { Transaction } from '@mysten/sui/transactions';
import { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519';
import * as dotenv from 'dotenv';
dotenv.config();

const keypair = Ed25519Keypair.fromSecretKey(process.env.KeyPair);
const address = keypair.getPublicKey().toSuiAddress();

console.log(address,'address');
const client = new SuiClient({ url: getFullnodeUrl('mainnet') });

// node init.js
const admin_cap = process.env.AdminCap;
const operator_cap = process.env.OperatorCap;


const package_id = process.env.HawalPackageId;
const us_staking = process.env.HawalStaking;


//  stakeing - nodeID
const wal_staking = process.env.WalrusStakingID;
// for youself coin
const coin_wal = "0xadef1198490f5add1d2096df96f4fc58865025654686de5609ff1eb709f7a745";
let tx2 = new Transaction();
tx2.moveCall({
	target: package_id+'::interface::request_stake',
	arguments: [
		tx2.object(wal_staking),
        tx2.object(us_staking),
        tx2.object(coin_wal),
        tx2.pure('id','0x0')
    ],
});

const result_request_stake = await client.signAndExecuteTransaction({
	transaction: tx2,
	signer: keypair,
});
console.log(result_request_stake,'result_request_stake');

// const wal_system = process.env.WalrusSystemID;
// // coin<HAWAL>
// const coin_hawal = "0x523e75ccda19943e7c224754f256829b442102c3c541d1717e41e7df691e6d7c";

// let tx3 = new Transaction();
// tx3.moveCall({
// 	target: package_id+'::interface::request_unstake_delay',
// 	arguments: [
// 		tx3.object(wal_system),
// 		tx3.object(wal_staking),
//         tx3.object(us_staking),
// 		tx3.object("0x6"),
//         tx3.object(coin_hawal),
//     ],
// });

// const result_request_unstake_delay = await client.signAndExecuteTransaction({
// 	transaction: tx3,
// 	signer: keypair,
// });
// console.log(result_request_unstake_delay,'result_request_unstake_delay');



// const unstakeTicket = "";
// let tx4 = new Transaction();
// tx4.moveCall({
// 	target: package_id+'::interface::claim',
// 	arguments: [
// 		tx4.object(wal_system),
// 		tx4.object(wal_staking),
//         tx4.object(us_staking),
// 		tx3.object("0x6"),
// 		tx3.object(unstakeTicket),
//     ],
// });

// const result_claim = await client.signAndExecuteTransaction({
// 	transaction: tx4,
// 	signer: keypair,
// });
// console.log(result_claim,'result_claim');