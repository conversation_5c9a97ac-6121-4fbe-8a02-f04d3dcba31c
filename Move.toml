[package]
name = "haedal"
version = "0.0.1"
edition = "2024"
published-at = "0x59830ad8bee26c509181edeefe0d75b1fb84ee7288277c2d52313b66c1be944c"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "mainnet", override = true }
#Walrus = { git = "https://github.com/MystenLabs/walrus.git", subdir = "contracts/walrus", rev = "main" }
#WAL = { git = "https://github.com/MystenLabs/walrus.git", subdir = "contracts/wal", rev = "main" }
Walrus = { local = "./local_wal/walrus" }
WAL = { local = "./local_wal/wal" }

[addresses]
haedal = "0x0"
sui = "0x2"
walrus = "0xfdc88f7d7cf30afab2f82e8380d11ee8f70efb90e863d1de8616fae1bb09ea77"
