#[test_only]
module haedal::hawal_instant_withdraw_test {
    use sui::balance;
    use haedal::{
        walstaking,
        manage,
        operate,
        interface
    };
    use sui::test_scenario::{Self, Scenario,ECantReturnObject};
    use haedal::hawal_common;
    use std::debug;
    use walrus::{
        staking::Staking,
        test_node, 
        test_utils,
        staked_wal,
        system::System
    };
    use haedal::hawal;
    use sui::coin;
    use sui::clock;
    
    const EPOCH_DURATION: u64 = 2 * 24 * 60 * 60 * 1000;
    const PARAM_SELECTION_DELTA: u64 = 2 * 24 * 60 * 60 * 1000 / 2;

    const MIN_STAKING_THRESHOLD: u64 = 1_000_000_000; // 1 WAL
    const ADMIN_ADDR:address = @0xA11CE;//admin
    const TEST_STAKER_1: address = @0x1234;
    const TEST_STAKER_2: address = @0x1235;

    // Scenario: node users N
    // Epoch 0: uA stakes 3000 N+1
    // Epoch 1: uB stakes 2000 N+1
    // Epoch 1: uB request_unstake_instant 2000
    #[test]
    fun test_instant_withdraw(){
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;

        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();

        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);

        wal_scenario.next_tx(TEST_STAKER_1);
        // mint 1000 wal
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(3100, ctx);
        interface::request_stake(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);

        hawal_common::print(b"epoch0.5");
        wal_scenario.next_tx(ADMIN_ADDR);
        clock_object.increment_for_testing(PARAM_SELECTION_DELTA + 1000);
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);

        // uB stake
        wal_scenario.next_tx(TEST_STAKER_2);
        let ctx = scenario.ctx();
        let coin2 = test_utils::mint_wal(2200, ctx);
        let res = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin2, excluded_node.node_id(), ctx);
        // uB request_unstake_instant
        wal_scenario.next_tx(TEST_STAKER_2);
        let ctx = scenario.ctx();
        interface::request_unstake_instant(&mut wal_system, &mut wal_staking, &mut staking_object, &clock_object, res, ctx);

        // check_fee
        let protocol_wal = walstaking::get_protocol_wal_vault_amount(&staking_object);
        debug::print(&protocol_wal);

        hawal_common::walrus_return_shared(wal_staking,wal_system);
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }


    // Scenario: node users N
    // Epoch 0: uA stakes 3000 N+1
    // Epoch 1: uB stakes 5000 N+1
    // Epoch 1: uB request_unstake_instant 8000 >abrot
    #[test,expected_failure(abort_code = haedal::walstaking::EUnstakeNeedAmountIsNotZero)]
    fun test_instant_withdraw_exceeds(){
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;

        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();

        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);

        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(3000, ctx);
        let res = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);

        hawal_common::print(b"epoch1");
        wal_scenario.next_tx(ADMIN_ADDR);
        clock_object.increment_for_testing(EPOCH_DURATION + PARAM_SELECTION_DELTA - 1000);
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);

        // uA stake
        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = scenario.ctx();
        let coin2 = test_utils::mint_wal(5000, ctx);
        let mut res1 = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin2, excluded_node.node_id(), ctx);

        // uA request_unstake_instant 8000
        wal_scenario.next_tx(TEST_STAKER_2);
        let ctx = scenario.ctx();
        res1.join(res);
        debug::print(&res1);
        interface::request_unstake_instant(&mut wal_system, &mut wal_staking, &mut staking_object, &clock_object, res1, ctx);

        hawal_common::walrus_return_shared(wal_staking,wal_system);
        
        // destory
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }


    #[test]
    fun test_total_instant_number(){
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;

        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();

        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        let mut excluded_node = nodes.pop_back();
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);

        wal_scenario.next_tx(TEST_STAKER_1);
        // mint 1000 wal
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(10000, ctx);
        interface::request_stake(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);

        // epoch1
        hawal_common::print(b"epoch1");
        wal_scenario.next_tx(ADMIN_ADDR);
        clock_object.increment_for_testing(EPOCH_DURATION + PARAM_SELECTION_DELTA - 1000);
        // epoch
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);

        wal_scenario.next_tx(TEST_STAKER_2);
        let ctx = scenario.ctx();
        let coin2 = test_utils::mint_wal(3000, ctx);
        interface::request_stake(&mut wal_staking, &mut staking_object, coin2, excluded_node.node_id(), ctx);
        
        // get total amount
        let total_amount = walstaking::get_total_instant_unstake_wal(&wal_system,&wal_staking,&staking_object);
        debug::print(&total_amount);

        hawal_common::walrus_return_shared(wal_staking,wal_system);
        // destory
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }
}
