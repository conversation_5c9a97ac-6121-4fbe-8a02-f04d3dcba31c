#[test_only]
module haedal::hawal_claim_test {
    use sui::balance;
    use std::unit_test::assert_eq;
    use haedal::{
        walstaking::{Self,UnstakeTicket},
        manage,
        operate,
        interface
    };
    use sui::test_scenario::{<PERSON>, <PERSON><PERSON><PERSON>,ECantReturnObject};
    use haedal::hawal_common::{Self, assert_gt, assert_lt};
    use std::debug;
    use walrus::{
        staking::Staking,
        test_utils,
        system::System
    };
    // use haedal::hawal::{HAWAL};
    use sui::coin::{Self, Coin, TreasuryCap};
    use sui::object;
    use sui::clock::{Self,Clock};
    
    const EPOCH_DURATION: u64 = 2 * 24 * 60 * 60 * 1000;
    const PARAM_SELECTION_DELTA: u64 = 2 * 24 * 60 * 60 * 1000 / 2;
    const EPOCH_ZERO_DURATION: u64 = 100000000;

    const MIN_STAKING_THRESHOLD: u64 = 1_000_000_000; // 1 WAL

    const ADMIN_ADDR:address = @0xA11CE;//admin
    const TEST_STAKER_1: address = @0x1234;
    const TEST_STAKER_2: address = @0xbaca112;

    // cliam
    #[test]
    fun test_claim_main(){
        
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;
        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();
        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);

        
        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(1, ctx);
        let res = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);
        assert!(walstaking::get_total_staked(&mut staking_object) == MIN_STAKING_THRESHOLD, 111);

        // epoch1
        hawal_common::print(b"epoch1");
        wal_scenario.next_tx(ADMIN_ADDR);
        clock_object.increment_for_testing(EPOCH_DURATION + PARAM_SELECTION_DELTA - 1000);
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        hawal_common::walrus_return_shared(wal_staking,wal_system);

        hawal_common::print(b"backup stake");
        runner.tx!(TEST_STAKER_2, |staking, system, ctx| {
            let coin5 = test_utils::mint_wal(50 , ctx);
            interface::request_stake(staking, &mut staking_object, coin5, excluded_node.node_id(), ctx);
        });
        
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, res, ctx);
        });
        let pool = walstaking::get_staked_pools(&mut staking_object,excluded_node.node_id());
        debug::print(pool);

        runner.scenario().next_tx(TEST_STAKER_1);
        let ticket = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();

        // epoch2
        hawal_common::print(b"epoch2");
        // wal_scenario.next_tx(ADMIN_ADDR);
        clock_object.increment_for_testing(EPOCH_DURATION + EPOCH_DURATION );
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(excluded_node.sui_address(), |staking, _, _| {
            staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        });

        //cliam
        hawal_common::print(b"claim");
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            interface::claim(system,staking, &mut staking_object,&clock_object, ticket, ctx);
        });

        assert!(walstaking::get_total_unstaked(&mut staking_object) == MIN_STAKING_THRESHOLD, 111);

        //destroy
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    
    #[test, expected_failure(abort_code = haedal::walstaking::EUnstakeNormalTicketLocking)]
    fun test_claim_not_rpoch(){
        
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;
        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();
        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);

        
        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(1, ctx);
        let res = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);
        assert!(walstaking::get_total_staked(&mut staking_object) == MIN_STAKING_THRESHOLD, 111);

        // epoch1
        hawal_common::print(b"epoch1");
        wal_scenario.next_tx(ADMIN_ADDR);
        clock_object.increment_for_testing(EPOCH_DURATION + PARAM_SELECTION_DELTA - 1000);
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        hawal_common::walrus_return_shared(wal_staking,wal_system);

        // request_withdraw_stake
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, res, ctx);
        });
        let pool = walstaking::get_staked_pools(&mut staking_object,excluded_node.node_id());
        debug::print(pool);

        runner.scenario().next_tx(TEST_STAKER_1);
        let ticket = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();

        
        hawal_common::print(b"claim");
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            interface::claim(system,staking, &mut staking_object,&clock_object, ticket, ctx);
        });

        assert!(walstaking::get_total_unstaked(&mut staking_object) == MIN_STAKING_THRESHOLD, 111);

        // destory
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    #[test, expected_failure(abort_code = haedal::walstaking::EUnstakeNormalTicketLocking)]
    fun test_claim_not_time_ms(){
        
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;
        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();
        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);

        
        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(1, ctx);
        let res = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);
        assert!(walstaking::get_total_staked(&mut staking_object) == MIN_STAKING_THRESHOLD, 111);

        // epoch1
        hawal_common::print(b"epoch1");
        wal_scenario.next_tx(ADMIN_ADDR);
        clock_object.increment_for_testing(EPOCH_DURATION + PARAM_SELECTION_DELTA - 1000);
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        hawal_common::walrus_return_shared(wal_staking,wal_system);

        hawal_common::print(b"backup stake");
        runner.tx!(TEST_STAKER_2, |staking, system, ctx| {
            let coin5 = test_utils::mint_wal(50 , ctx);
            interface::request_stake(staking, &mut staking_object, coin5, excluded_node.node_id(), ctx);
        });

        // request_withdraw_stake
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, res, ctx);
        });
        let pool = walstaking::get_staked_pools(&mut staking_object,excluded_node.node_id());
        debug::print(pool);

        runner.scenario().next_tx(TEST_STAKER_1);
        let ticket = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();

        // epoch2
        hawal_common::print(b"epoch2");
        clock_object.increment_for_testing(EPOCH_DURATION + PARAM_SELECTION_DELTA - 200 );
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(excluded_node.sui_address(), |staking, _, _| {
            staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        });

        
        let clock = clock::create_for_testing(test_scenario::ctx(scenario));
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            interface::claim(system,staking, &mut staking_object,&clock, ticket, ctx);
        });

        assert!(walstaking::get_total_unstaked(&mut staking_object) == MIN_STAKING_THRESHOLD, 111);

        // destory
        clock::destroy_for_testing(clock);
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }


    // stop
    #[test, expected_failure(abort_code = haedal::walstaking::EClaimPause)]
    fun test_stop_claim(){
        
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;
        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();
        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);

        operate::toggle_claim(&operate_cap,&mut staking_object,true);

        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(1, ctx);
        let res = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);
        assert!(walstaking::get_total_staked(&mut staking_object) == MIN_STAKING_THRESHOLD, 111);

        // epoch1
        hawal_common::print(b"epoch1");
        wal_scenario.next_tx(ADMIN_ADDR);
        clock_object.increment_for_testing(EPOCH_DURATION + PARAM_SELECTION_DELTA - 1000);
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        hawal_common::walrus_return_shared(wal_staking,wal_system);

        // request_withdraw_stake
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, res, ctx);
        });
        let pool = walstaking::get_staked_pools(&mut staking_object,excluded_node.node_id());
        debug::print(pool);

        runner.scenario().next_tx(TEST_STAKER_1);
        let ticket = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();

        hawal_common::print(b"claim");
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            interface::claim(system,staking, &mut staking_object,&clock_object, ticket, ctx);
        });

        // destory
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        // hawal_common::walrus_return_shared(wal_staking,wal_system);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    // amount error
    // #[test, expected_failure(abort_code = haedal::walstaking::EUnstakeNeedAmountIsNotZero)]
    // fun test_claim_amount_exceed_max(){
    //     
    //     let (mut scenario_val,mut staking_object,admin_cap,operate_cap, clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
    //     
    //     let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
    //     let wal_scenario = runner.scenario();
    //     let mut excluded_node = nodes.pop_back();

    //     let coin = coin::mint_for_testing(10000, wal_scenario.ctx());        
    //     runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
    //         walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, coin, ctx);
    //     });
        
    //     // destory
    //     hawal_common::walrus_destroy(nodes,excluded_node,runner);
    //     hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    // }
}
