#[test_only]
module haedal::hawal_stake_test {
    use haedal::{
        walstaking,
        operate,
        interface
    };
    use sui::test_scenario::{<PERSON>enario};
    use haedal::hawal_common;
    use std::debug;
    use walrus::{
        staking::Staking,
        test_utils,
        staked_wal::{Self,StakedWal},
        system::System
    };
    use haedal::hawal::{HAWAL};
    use sui::coin::{Self, Coin, TreasuryCap};
    use sui::object;
    
    const EPOCH_DURATION: u64 = 2 * 24 * 60 * 60 * 1000;

    const MIN_STAKING_THRESHOLD: u64 = 1_000_000_000; // 1 WAL

    const ADMIN_ADDR:address = @0xA11CE;//admin
    const TEST_STAKER_1: address = @0x1234;
    const TEST_STAKER_2: address = @0x1235;
    const TEST_STAKER_3: address = @0x1236;
    const TEST_STAKER_4: address = @0x1237;
    // const TEST_STAKER_5: address = @0x1238;


    #[test, expected_failure(abort_code = haedal::walstaking::EStakeActiveValidatorsNotFound)]
    
    fun test_empty_active_validators(){
        
        let (mut scenario_val,mut staking_object,admin_cap,operate_cap, clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        // let scenario = &mut scenario_val;

        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();
        // 
        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        
        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = wal_scenario.ctx();
        let coin1 = test_utils::mint_wal(2 * MIN_STAKING_THRESHOLD, ctx);
        interface::request_stake(&mut wal_staking, &mut staking_object, coin1, object::id_from_address(@0x0), ctx);

        // destory
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::walrus_return_shared(wal_staking,wal_system);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    #[test]
    fun test_select_active_validators(){
        
        let (mut scenario_val,mut staking_object,admin_cap,operate_cap, clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        // let scenario = &mut scenario_val;
        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();
        // 
        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        hawal_common::print(b"init end");
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);
        hawal_common::print(b"set_active_validators");
        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = wal_scenario.ctx();
        let coin = test_utils::mint_wal(1, ctx);

        let res = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);

        assert!(walstaking::get_staked_validator(&mut staking_object,excluded_node.node_id()), 111);
        // hawal balance
        assert!(walstaking::get_hawal_supply(&mut staking_object) == MIN_STAKING_THRESHOLD, 111);
        assert!(coin::value(&res) == MIN_STAKING_THRESHOLD, 111);

        // test_scenario::return_shared(res);
        transfer::public_transfer(res,tx_context::sender(ctx));
        // destory
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::walrus_return_shared(wal_staking,wal_system);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    #[test]
    fun test_none_validators(){
        
        let (mut scenario_val,mut staking_object,admin_cap,operate_cap, clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        // let scenario = &mut scenario_val;
        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();
        // 
        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        let mut excluded_node1  = nodes.pop_back();
        hawal_common::print(b"init end");

        wal_scenario.next_tx(ADMIN_ADDR);
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id(),excluded_node1.node_id()]);
        hawal_common::print(b"set_active_validators");
        
        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = wal_scenario.ctx();
        let coin = test_utils::mint_wal(2, ctx);
        interface::request_stake(&mut wal_staking, &mut staking_object, coin, object::id_from_address(@0x0), ctx);

        
        assert!(walstaking::get_staked_validator(&mut staking_object,excluded_node.node_id()), 111);
        
        assert!(walstaking::get_hawal_supply(&mut staking_object) == MIN_STAKING_THRESHOLD * 2, 111);

        
        wal_scenario.next_tx(TEST_STAKER_2);
        let ctx = wal_scenario.ctx();
        let coin1 = test_utils::mint_wal(2, ctx);
        interface::request_stake(&mut wal_staking, &mut staking_object, coin1, object::id_from_address(@0x0), ctx);

        
        assert!(walstaking::get_staked_validator(&mut staking_object,excluded_node1.node_id()), 111);
        assert!(walstaking::get_hawal_supply(&mut staking_object) == MIN_STAKING_THRESHOLD * 4, 111);

        
        wal_scenario.next_tx(TEST_STAKER_3);
        let ctx = wal_scenario.ctx();
        let coin2 = test_utils::mint_wal(2, ctx);
        interface::request_stake(&mut wal_staking, &mut staking_object, coin2, object::id_from_address(@0x0), ctx);
        
        assert!(walstaking::get_hawal_supply(&mut staking_object) == MIN_STAKING_THRESHOLD * 6, 111);

        // destory
        excluded_node1.destroy();
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::walrus_return_shared(wal_staking,wal_system);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    #[test]
    fun test_epoch_join_pools(){
        
        let (mut scenario_val,mut staking_object,admin_cap,operate_cap, clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;

        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();

        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);

        
        wal_scenario.next_tx(TEST_STAKER_1);
        
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(1, ctx);
        interface::request_stake(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);
        hawal_common::print(b"stake_with_pool1 end");


        wal_scenario.next_tx(TEST_STAKER_2);
        let ctx = scenario.ctx();
        let coin1 = test_utils::mint_wal(2, ctx);
        interface::request_stake(&mut wal_staking, &mut staking_object, coin1, excluded_node.node_id(), ctx);

        // epoch1
        
        hawal_common::print(b"epoch1");
        wal_scenario.next_tx(ADMIN_ADDR);
        runner.clock().increment_for_testing(EPOCH_DURATION);
        
        wal_staking.voting_end(runner.clock());
        wal_staking.initiate_epoch_change(&mut wal_system, runner.clock());
        
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node.cap_mut(), epoch, runner.clock());

        
        runner.scenario().next_tx(TEST_STAKER_2);
        let ctx = scenario.ctx();
        let coin2 = test_utils::mint_wal(3, ctx);
        interface::request_stake(&mut wal_staking, &mut staking_object, coin2, excluded_node.node_id(), ctx);
        
        assert!(walstaking::get_hawal_supply(&mut staking_object) == MIN_STAKING_THRESHOLD * 6, 111);
        let pool = walstaking::get_staked_pools(&mut staking_object,excluded_node.node_id());
        debug::print(pool);

        // destory
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::walrus_return_shared(wal_staking,wal_system);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    #[test, expected_failure(abort_code = haedal::walstaking::EStakePause)]
    fun test_stop_stake(){
        
        let (mut scenario_val,mut staking_object,admin_cap,operate_cap, clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);

        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();
        // 
        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        hawal_common::print(b"init end");
        
        wal_scenario.next_tx(ADMIN_ADDR);
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);
        hawal_common::print(b"set_active_validators");
        
        operate::toggle_stake(&operate_cap,&mut staking_object,true);
        
        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = wal_scenario.ctx();
        let coin = test_utils::mint_wal(1, ctx);
        interface::request_stake(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);
        
        // destory
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::walrus_return_shared(wal_staking,wal_system);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    #[test, expected_failure(abort_code = haedal::walstaking::EStakeNotEnoughWal)]
    fun test_min_amount(){
        
        let (mut scenario_val,mut staking_object,admin_cap,operate_cap, clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();
        
        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        hawal_common::print(b"init end");
        // 
        wal_scenario.next_tx(ADMIN_ADDR);
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);
        hawal_common::print(b"set_active_validators");
        //
        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = wal_scenario.ctx();
        let coin = test_utils::mint_frost(1000, ctx);
        interface::request_stake(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);
        
        // destory
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::walrus_return_shared(wal_staking,wal_system);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }
}
