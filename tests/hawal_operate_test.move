#[test_only]
module haedal::test_operate {
    use sui::clock;
    use sui::coin;
    use std::debug;
    use sui::test_scenario::{<PERSON>, <PERSON><PERSON><PERSON>};
    use haedal::{
        walstaking,
        manage,
        operate,
        interface,
        config,
        hawal
    };
    use walrus::{
        staking::Staking,
        test_utils,
        staked_wal::{Self,StakedWal},
        system::System
    };
    use haedal::hawal_common;

    const ADMIN_ADDR:address = @0x1234;//admin
    const TEST_STAKER_1: address = @0x1234;
    const TEST_STAKER_2: address = @0x1235;
    const TEST_STAKER_3: address = @0x1236;

    const EPOCH_DURATION: u64 = 14 * 24 * 60 * 60 * 1000;
    const PARAM_SELECTION_DELTA: u64 = (14 * 24 * 60 * 60 * 1000) / 2;

    #[test]
    fun test_configs() {
        
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;

        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();
        let mut excluded_node = nodes.pop_back();
        

        // test config setters
        test_scenario::next_tx(scenario, ADMIN_ADDR);
        {
            operate::set_deposit_fee(&operate_cap, &mut staking_object, 100);
            assert!(100 == config::get_deposit_fee(walstaking::get_config_mut(&mut staking_object)), 2);
            
            operate::set_reward_fee(&operate_cap, &mut staking_object, 200);
            assert!(200 == config::get_reward_fee(walstaking::get_config_mut(&mut staking_object)), 3);
            
            operate::set_validator_reward_fee(&operate_cap, &mut staking_object, 300);
            assert!(300 == config::get_validator_reward_fee(walstaking::get_config_mut(&mut staking_object)), 4);
            
            operate::set_service_fee(&operate_cap, &mut staking_object, 400);
            assert!(400 == config::get_service_fee(walstaking::get_config_mut(&mut staking_object)), 5);

            operate::set_withdraw_time_limit(&operate_cap, &mut staking_object, 22*3600*1000);
            assert!(22*3600*1000 == config::get_withdraw_time_limit(walstaking::get_config_mut(&mut staking_object)), 6);

            operate::set_validator_count(&operate_cap, &mut staking_object, 20);
            assert!(20 == config::get_validator_count(walstaking::get_config_mut(&mut staking_object)), 7);

            let mut pause_claim = false;
            assert!(pause_claim == walstaking::query_pause_claim(&staking_object), 9);
            
            operate::toggle_claim(&operate_cap, &mut staking_object, pause_claim);
            assert!(pause_claim == walstaking::query_pause_claim(&staking_object), 10);

            pause_claim = true;
            operate::toggle_claim(&operate_cap, &mut staking_object, pause_claim);
            assert!(pause_claim == walstaking::query_pause_claim(&staking_object), 11);
        };
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    
    #[test]
    fun test_update_rewards_main(){
        
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        // let scenario = &mut scenario_val;
        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        // let wal_scenario = runner.scenario();
        let excluded_node = nodes.pop_back();

        nodes.do_mut!(|node| {
            runner.tx!(node.sui_address(), |staking, system, ctx| {
                operate::update_validator_rewards(&operate_cap, &mut staking_object, system, staking, node.node_id(), ctx);
            });
        });

        // destory
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }
}
