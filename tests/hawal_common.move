#[test_only]
module haedal::hawal_common {
    
    use std::vector;
    use std::debug;

    use sui::clock;
    use sui::coin;
    use sui::test_scenario::{Self, Sc<PERSON>rio};
    use sui::address;
    // use sui::test_utils;
    use sui::table::{Self, Table};
    // use sui::test_utils::assert_eq;

    use haedal::hawal;
    use haedal::walstaking;
    use haedal::manage;
    use haedal::operate;
    use haedal::config;

    use walrus::{
        e2e_runner::{Self,TestRunner}, 
        test_node::{Self, TestStorageNode},
        staking::Staking,
        system::System
    };
    
    const STAKER_ADDR_1: address = @0x11;
    const STAKER_ADDR_2: address = @0x22;
    const STAKER_ADDR_3: address = @0x33;
    const STAKER_ADDR_4: address = @0x44;

    const MIST_PER_SUI: u64 = 1_000_000_000;

    const EPOCH_DURATION: u64 = 14 * 24 * 60 * 60 * 1000;
    const EPOCH_ZERO_DURATION: u64 = 10000;

    const COMMISSION_RATE: u16 = 0;
    const STORAGE_PRICE: u64 = 5;
    const WRITE_PRICE: u64 = 1;
    const NODE_CAPACITY: u64 = 1_000_000_000;
    const N_SHARDS: u16 = 100;
    const DEFAULT_WALRUS_START_TIME: u64 = 1741705204744;

    public fun hawal_test_setup(tester: address): (Scenario, walstaking::Staking, manage::AdminCap, manage::OperatorCap, clock::Clock) {
        let mut scenario_object = test_scenario::begin(tester);
        let scenario = &mut scenario_object;

        // init clock
        test_scenario::next_tx(scenario,tester);
        {
            let mut clock = clock::create_for_testing(test_scenario::ctx(scenario));
            clock.set_for_testing(DEFAULT_WALRUS_START_TIME);
            clock::share_for_testing(clock);
        };


        // mock deploy
        test_scenario::next_tx(scenario, tester);
        {
            hawal::init_hawal_for_test(test_scenario::ctx(scenario));
            manage::init_staking_for_test(test_scenario::ctx(scenario));
        };

        // initialize
        test_scenario::next_tx(scenario, tester);
        {
            let mut admin_cap = test_scenario::take_from_sender<manage::AdminCap>(scenario);
            let treasury = test_scenario::take_from_sender<coin::TreasuryCap<hawal::HAWAL>>(scenario);
            manage::initialize(&mut admin_cap, treasury, test_scenario::ctx(scenario));
            manage::set_operator_cap_to_address(&mut admin_cap, tester,test_scenario::ctx(scenario));

            test_scenario::return_to_sender(scenario, admin_cap);
        };

        // commit the above transaction
        test_scenario::next_tx(scenario, tester);

        // get the objects
        let clock_object = test_scenario::take_shared<clock::Clock>(scenario);
        let staking_object = test_scenario::take_shared<walstaking::Staking>(scenario);
        let admin_cap = test_scenario::take_from_sender<manage::AdminCap>(scenario);
        let operate_cap = test_scenario::take_from_sender<manage::OperatorCap>(scenario);

        (scenario_object, staking_object, admin_cap, operate_cap, clock_object)
    }

    public fun walrus_test_setup(admin:address):(vector<TestStorageNode>,TestRunner) {
        // 1 10 test nodes
        let mut nodes = test_node::test_nodes();
        // 2 walrus-runner 
        let mut runner = e2e_runner::prepare(admin)
            .epoch_zero_duration(EPOCH_ZERO_DURATION)
            .epoch_duration(EPOCH_DURATION)
            .n_shards(N_SHARDS)
            .build();


        let epoch = runner.epoch();
        nodes.do_mut!(|node| {
            runner.tx!(node.sui_address(), |staking, _, ctx| {
                let cap = staking.register_candidate(
                    node.name(),
                    node.network_address(),
                    node.metadata(),
                    node.bls_pk(),
                    node.network_key(),
                    node.create_proof_of_possession(epoch),
                    COMMISSION_RATE,
                    STORAGE_PRICE,
                    WRITE_PRICE,
                    NODE_CAPACITY,
                    ctx,
                );
                node.set_storage_node_cap(cap);
            });
        });

        (nodes,runner)
    }

    public fun walrus_test_setup_us_shards(admin:address):(vector<TestStorageNode>,TestRunner) {
        // 1 10 test nodes
        let mut nodes = test_node::test_nodes();
        // 2 walrus-runner 
        let mut runner = e2e_runner::prepare(admin)
            .epoch_zero_duration(EPOCH_ZERO_DURATION)
            .epoch_duration(EPOCH_DURATION)
            .n_shards(2)
            .build();


        let epoch = runner.epoch();
        nodes.do_mut!(|node| {
            runner.tx!(node.sui_address(), |staking, _, ctx| {
                let cap = staking.register_candidate(
                    node.name(),
                    node.network_address(),
                    node.metadata(),
                    node.bls_pk(),
                    node.network_key(),
                    node.create_proof_of_possession(epoch),
                    COMMISSION_RATE,
                    STORAGE_PRICE,
                    WRITE_PRICE,
                    NODE_CAPACITY,
                    ctx,
                );
                node.set_storage_node_cap(cap);
            });
        });

        (nodes,runner)
    }

    // destory walrus
    public fun walrus_destroy(
        nodes:vector<TestStorageNode>,
        excluded_node:TestStorageNode,
        runner:TestRunner,
    ){
        nodes.destroy!(|node| node.destroy());
        excluded_node.destroy();
        runner.destroy();
    }

    // destory walrus-system staking
    public fun walrus_return_shared(
        wal_staking:Staking,
        wal_system:System
    ){
        test_scenario::return_shared(wal_staking);
        test_scenario::return_shared(wal_system);
    }
    
    public fun hawal_test_tear_down(scenario: Scenario, staking_object: walstaking::Staking, admin_cap: manage::AdminCap, operate_cap:manage::OperatorCap, clock_object: clock::Clock) {
        test_scenario::return_shared(staking_object);
        test_scenario::return_to_sender(&scenario, admin_cap);
        test_scenario::return_to_sender(&scenario, operate_cap);
        clock_object.destroy_for_testing();
        test_scenario::end(scenario);
    }

    public fun assert_gt(t1: u64, t2: u64) {
        let res = t1 > t2;
        if (!res) {
            print(b"assert_gt Assertion failed:");
            std::debug::print(&t1);
            
            std::debug::print(&t2);
            abort(0)
        }
    }

    public fun assert_lt(t1: u64, t2: u64) {
        let res = t1 < t2;
        if (!res) {
            print(b"assert_lt Assertion failed:");
            std::debug::print(&t1);
            print(b">=");
            std::debug::print(&t2);
            abort(0)
        }
    }

    public fun print(str: vector<u8>) {
        std::debug::print(&std::ascii::string(str))
    }
}
