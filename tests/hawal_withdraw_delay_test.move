#[test_only]
module haedal::hawal_withdraw_test {
    use sui::balance;
    // use std::unit_test::assert_eq;
    use haedal::{
        walstaking,
        manage,
        operate,
        interface
    };
    use sui::test_scenario::{<PERSON>, <PERSON><PERSON><PERSON>,ECantReturnObject};
    use haedal::hawal_common;
    use std::debug;
    use walrus::{
        staking::Staking,
        test_node, 
        test_utils,
        staked_wal,
        system::System
    };
    use haedal::hawal;
    use sui::coin;
    use sui::clock;
    
    const EPOCH_DURATION: u64 = 2 * 24 * 60 * 60 * 1000;
    const PARAM_SELECTION_DELTA: u64 = 2 * 24 * 60 * 60 * 1000 / 2;

    const MIN_STAKING_THRESHOLD: u64 = 1_000_000_000; // 1 WAL
    const ADMIN_ADDR:address = @0xA11CE;//admin
    const TEST_STAKER_1: address = @0x1234;
    const TEST_STAKER_2: address = @0x1235;

    //  - epoch N+1 
    #[test, expected_failure(abort_code = ECantReturnObject)]
    fun test_request_unstake_delay_n_1(){
        
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;

        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();

        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        let mut excluded_node_1 = nodes.pop_back();
        
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id(),excluded_node_1.node_id()]);

        
        wal_scenario.next_tx(TEST_STAKER_1);
        // mint 1000WAL
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(1, ctx);
        let res = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);
        
        hawal_common::print(b"stake_with_pool1 end");

        // mint 1000WAL
        wal_scenario.next_tx(TEST_STAKER_2);
        let ctx = scenario.ctx();
        let coin1 = test_utils::mint_wal(1, ctx);
        let res1 = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin1, excluded_node_1.node_id(), ctx);
        
        hawal_common::print(b"stake_with_pool2 end");

        // epoch1
        hawal_common::print(b"epoch1");
        wal_scenario.next_tx(ADMIN_ADDR);
        clock_object.increment_for_testing(EPOCH_DURATION + PARAM_SELECTION_DELTA - 1000);
        // epoch
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        // 
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        hawal_common::walrus_return_shared(wal_staking,wal_system);
        
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, res, ctx);
        });

        runner.scenario().next_tx(TEST_STAKER_1);
        let ticket = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();
        

        // destory
        test_scenario::return_shared(res1);
        excluded_node_1.destroy();
        test_scenario::return_to_sender(runner.scenario(), ticket);
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    #[test]
    fun test_request_unstake_delay_n_2(){
        
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;

        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();

        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);

        
        wal_scenario.next_tx(TEST_STAKER_1);
        // mint 1000 wal
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(1, ctx);
        let res = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);

        // epoch1
        hawal_common::print(b"epoch1");
        wal_scenario.next_tx(ADMIN_ADDR);
        clock_object.increment_for_testing(EPOCH_DURATION + PARAM_SELECTION_DELTA - 1000);
        // epoch
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        // 
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);

        //
        wal_scenario.next_tx(TEST_STAKER_2);
        let ctx = scenario.ctx();
        let coin2 = test_utils::mint_wal(3, ctx);
        interface::request_stake(&mut wal_staking, &mut staking_object, coin2, excluded_node.node_id(), ctx);

        // epoch2
        hawal_common::print(b"epoch2");
        wal_scenario.next_tx(ADMIN_ADDR);
        clock_object.increment_for_testing(EPOCH_DURATION);
        // epoch
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        // 
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        //  N+2
        debug::print(&epoch);
        hawal_common::walrus_return_shared(wal_staking,wal_system);

        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, res, ctx);
        });

        runner.scenario().next_tx(TEST_STAKER_1);
        let ticket = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();
        

        // destory
        test_scenario::return_to_sender(runner.scenario(), ticket);
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    //
    #[test,expected_failure(abort_code = haedal::walstaking::EUnstakeOutOfRange)]
    fun test_unstake_out_range(){
        
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;
        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();
        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);

        
        wal_scenario.next_tx(TEST_STAKER_1);
        // mint 1000 wal
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(1, ctx);
        let res = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);

        // epoch1
        hawal_common::print(b"epoch1");
        wal_scenario.next_tx(ADMIN_ADDR);
        clock_object.increment_for_testing(EPOCH_DURATION + PARAM_SELECTION_DELTA - 1000);
        // epoch
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        // 
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        hawal_common::walrus_return_shared(wal_staking,wal_system);

        let mut clock = clock::create_for_testing(wal_scenario.ctx());
        clock.set_for_testing(0);
        debug::print(&clock);
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock, res, ctx);
        });

        runner.scenario().next_tx(TEST_STAKER_1);
        let ticket = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();
        

        // destory
        test_scenario::return_shared(clock);
        test_scenario::return_to_sender(runner.scenario(), ticket);
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    #[test]
    fun test_unstake_node_not_commit(){
        
        
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;

        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();

        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);

        
        wal_scenario.next_tx(TEST_STAKER_1);
        // mint 1000 wal
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(1, ctx);
        let res = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);
        
        hawal_common::print(b"stake_with_pool1 end");

        // epoch1
        hawal_common::print(b"epoch1");
        wal_scenario.next_tx(ADMIN_ADDR);
        clock_object.increment_for_testing(EPOCH_DURATION + PARAM_SELECTION_DELTA - 1000);
        // epoch
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        // 
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        hawal_common::walrus_return_shared(wal_staking,wal_system);
        
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, res, ctx);
        });

        // runner.scenario().next_tx(TEST_STAKER_1);
        // let ticket = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();
        // 

        // destory
        // test_scenario::return_to_sender(runner.scenario(), ticket);
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    // stop
    #[test, expected_failure(abort_code = haedal::walstaking::EUnstakePause)]
    fun test_stop_unstake(){
        
        let (scenario_val,mut staking_object,admin_cap,operate_cap, clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();
        let excluded_node = nodes.pop_back();

        operate::toggle_unstake(&operate_cap,&mut staking_object,true);

        let coin = coin::mint_for_testing(1000000000, wal_scenario.ctx());
        
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, coin, ctx);
        });
        
        // destory
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        // hawal_common::walrus_return_shared(wal_staking,wal_system);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    
    #[test, expected_failure(abort_code = haedal::walstaking::EUnstakeNotZeroHawal)]
    fun test_min_amount(){
        
        let (scenario_val,mut staking_object,admin_cap,operate_cap, clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();
        let excluded_node = nodes.pop_back();

        //
        let coin = coin::mint_for_testing(0, wal_scenario.ctx());        
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, coin, ctx);
        });
        
        // destory
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    // 
    #[test, expected_failure(abort_code = haedal::walstaking::EUnstakeExceedMinWalAmount)]
    fun test_amount_exceed_max(){
        
        let (scenario_val,mut staking_object,admin_cap,operate_cap, clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();
        let excluded_node = nodes.pop_back();
        
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            let coin = coin::mint_for_testing(10000, ctx);        
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, coin, ctx);
        });
        
        // destory
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    // // amount error
    // #[test, expected_failure(abort_code = haedal::walstaking::EUnstakeNeedAmountIsNotZero)]
    // fun test_need_amount_zero(){
    //     
    //     let (mut scenario_val,mut staking_object,admin_cap,operate_cap, clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
    //     
    //     let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
    //     let wal_scenario = runner.scenario();
    //     let mut excluded_node = nodes.pop_back();

    //     //
    //     let coin = coin::mint_for_testing(10000, wal_scenario.ctx());        
    //     runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
    //         walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, coin, ctx);
    //     });
        
    //     // destory
    //     hawal_common::walrus_destroy(nodes,excluded_node,runner);
    //     hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    // }
}
