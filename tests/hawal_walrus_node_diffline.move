#[test_only]
module haedal::test_node_diffline {
    use sui::clock;
    use sui::coin;
    use std::debug;
    use sui::test_scenario::{Self, <PERSON><PERSON><PERSON>};
    use haedal::{
        walstaking,
        manage,
        operate,
        interface,
        config,
        hawal
    };
    use walrus::{
        staking::Staking,
        test_utils,
        staked_wal::{Self,StakedWal},
        system::System
    };
    use haedal::hawal_common;

    const ADMIN_ADDR:address = @0x1234;//admin
    const TEST_STAKER_1: address = @0x1234;
    const TEST_STAKER_2: address = @0x1235;
    const TEST_STAKER_3: address = @0x1236;

    const EPOCH_DURATION: u64 = 14 * 24 * 60 * 60 * 1000;
    const PARAM_SELECTION_DELTA: u64 = (14 * 24 * 60 * 60 * 1000) / 2;

    // Scenario: node diff for not stake
    // Epoch 0: uA stakes 50 node1 N+1
    // Epoch 1: uB stakes 60 node2 N+1
    // Epoch 1: uC stakes 70 node3 N+1
    // Epoch 1: uA unstake 50 node1 N+1
    // epoch change--node1 diff line -- 
    // Epoch 2: operate::validator_offline
    // Check Node2 diff Node1
    // Check validators
    #[test]
    fun test_validator_offline_for_not_stake(){
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);

        let mut excluded_node_1 = nodes.pop_back();
        let mut excluded_node_2 = nodes.pop_back();
        let mut excluded_node_3 = nodes.pop_back();
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node_1.node_id(),excluded_node_2.node_id(),excluded_node_3.node_id()]);

        let wal_scenario = runner.scenario();
        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();

        // uA 
        hawal_common::print(b"ua stake");
        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(50 , ctx);
        let hawal_1 = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin, excluded_node_1.node_id(), ctx);

        hawal_common::print(b"epoch1");
        wal_scenario.next_tx(ADMIN_ADDR);
        // 1.1
        clock_object.increment_for_testing(EPOCH_DURATION);
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node_1.cap_mut(), epoch, &clock_object);
        debug::print(&epoch);
        hawal_common::walrus_return_shared(wal_staking,wal_system);

        hawal_common::print(b"ub stake");
        runner.tx!(TEST_STAKER_2, |staking, system, ctx| {
            let coin5 = test_utils::mint_wal(60 , ctx);
            interface::request_stake(staking, &mut staking_object, coin5, excluded_node_2.node_id(), ctx);
        });

        hawal_common::print(b"uc stake");
        runner.tx!(TEST_STAKER_3, |staking, system, ctx| {
            let coin5 = test_utils::mint_wal(70 , ctx);
            interface::request_stake(staking, &mut staking_object, coin5, excluded_node_3.node_id(), ctx);
        });

        hawal_common::print(b"ua unstake delay");
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            interface::request_unstake_delay(system,staking, &mut staking_object, &clock_object, hawal_1, ctx);
        });

        // epoch2
        hawal_common::print(b"epoch2");
        clock_object.increment_for_testing(EPOCH_DURATION);
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(ADMIN_ADDR, |staking, _, _| {
            staking.epoch_sync_done(excluded_node_2.cap_mut(), epoch, &clock_object);
            staking.epoch_sync_done(excluded_node_3.cap_mut(), epoch, &clock_object);
        });
        debug::print(&epoch);
        // check is offline
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            hawal_common::print(b"before");
            let pool = walstaking::get_staked_pools(&mut staking_object,excluded_node_1.node_id());
            debug::print(pool);

            // validator_offline node_1
            operate::validator_offline(&operate_cap, system, staking, &mut staking_object, excluded_node_1.node_id(), ctx);
            
            // wal_vault change
            debug::print(&staking_object);
        });
        
        // destory
        excluded_node_2.destroy();
        excluded_node_3.destroy();
        hawal_common::walrus_destroy(nodes,excluded_node_1,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }


    // Scenario: node diff for max shards
    // Epoch 0: uA stakes 50 node1 N+1
    // Epoch 1: uB stakes 60 node2 N+1
    // Epoch 1: uC stakes 70 node3 N+1
    // epoch change--node1 diff line-- 
    // Epoch 2: operate::validator_offline
    // Check Node2 diff Node1
    // Check validators
    #[test]
    fun test_validator_offline_for_max_shards(){
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup_us_shards(ADMIN_ADDR);

        let mut excluded_node_1 = nodes.pop_back();
        let mut excluded_node_2 = nodes.pop_back();
        let mut excluded_node_3 = nodes.pop_back();
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node_1.node_id(),excluded_node_2.node_id(),excluded_node_3.node_id()]);

        let wal_scenario = runner.scenario();
        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();

        // uA 
        hawal_common::print(b"epoch0");
        hawal_common::print(b"ua stake");
        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(50 , ctx);
        interface::request_stake(&mut wal_staking, &mut staking_object, coin, excluded_node_1.node_id(), ctx);

        hawal_common::print(b"epoch1");
        wal_scenario.next_tx(ADMIN_ADDR);
        // 1.1
        clock_object.increment_for_testing(EPOCH_DURATION);
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node_1.cap_mut(), epoch, &clock_object);
        debug::print(&epoch);
        hawal_common::walrus_return_shared(wal_staking,wal_system);

        hawal_common::print(b"ub stake");
        runner.tx!(TEST_STAKER_2, |staking, system, ctx| {
            let coin5 = test_utils::mint_wal(60 , ctx);
            interface::request_stake(staking, &mut staking_object, coin5, excluded_node_2.node_id(), ctx);
        });

        hawal_common::print(b"uc stake");
        runner.tx!(TEST_STAKER_3, |staking, system, ctx| {
            let coin5 = test_utils::mint_wal(70 , ctx);
            interface::request_stake(staking, &mut staking_object, coin5, excluded_node_3.node_id(), ctx);
        });

        // epoch2
        hawal_common::print(b"epoch2");
        clock_object.increment_for_testing(EPOCH_DURATION);
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(ADMIN_ADDR, |staking, _, _| {
            staking.epoch_sync_done(excluded_node_2.cap_mut(), epoch, &clock_object);
            staking.epoch_sync_done(excluded_node_3.cap_mut(), epoch, &clock_object);
        });
        debug::print(&epoch);
        // check is offline
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            hawal_common::print(b"before");
            let pool = walstaking::get_staked_pools(&mut staking_object,excluded_node_1.node_id());
            debug::print(pool);

            let pool2 = walstaking::get_staked_pools(&mut staking_object,excluded_node_2.node_id());
            debug::print(pool2);
            // validator_offline node_1
            operate::validator_offline(&operate_cap, system, staking, &mut staking_object, excluded_node_1.node_id(), ctx);

            // staked change
            let pool2 = walstaking::get_staked_pools(&mut staking_object,excluded_node_2.node_id());
            debug::print(pool2);
        });
        
        // destory
        excluded_node_2.destroy();
        excluded_node_3.destroy();
        hawal_common::walrus_destroy(nodes,excluded_node_1,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }
}
