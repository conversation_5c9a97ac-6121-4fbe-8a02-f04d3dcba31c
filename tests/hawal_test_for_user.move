#[test_only]
module haedal::hawal_test_for_user {
    use std::debug;
    use haedal::{
        walstaking::{Self,UnstakeTicket},
        manage,
        operate,
        interface
    };
    use sui::test_scenario::{<PERSON>, <PERSON><PERSON><PERSON>,ECantReturnObject};
    use haedal::hawal_common;
    use walrus::{
        staking::Staking,
        test_utils,
        staked_wal::{Self,StakedWal},
        system::System
    };
    use sui::clock::{Self,Clock};
    
    const EPOCH_DURATION: u64 = 2 * 24 * 60 * 60 * 1000;
    const PARAM_SELECTION_DELTA: u64 = (2 * 24 * 60 * 60 * 1000) / 2;

    const ADMIN_ADDR:address = @0xA11CE;//admin
    const TEST_STAKER_1: address = @0x1234;

    #[test]
    fun test_main_for_user(){
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
         // nodes
        let mut excluded_node = nodes.pop_back();
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);

         // test config setters
        runner.tx!(TEST_STAKER_1, |_, system, ctx| {
            let (current_epoch_start,mid_epoch_time,current_epoch_end,walrus_epoch_duration,walrus_current_epoch) = walstaking::get_epoch_time_ms_and_epoch(system,&mut staking_object);
            hawal_common::print(b"12333");
            debug::print(&current_epoch_start);
            debug::print(&mid_epoch_time);
            debug::print(&current_epoch_end);
            debug::print(&walrus_epoch_duration);
            debug::print(&walrus_current_epoch);
        });

        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }
    
}
