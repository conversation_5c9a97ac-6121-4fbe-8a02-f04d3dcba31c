#[test_only]
module haedal::hawal_main_test {
    use sui::balance;
    use haedal::{
        walstaking::{Self,UnstakeTicket},
        manage,
        operate,
        interface
    };
    use sui::test_scenario::{Self, Scenario,ECantReturnObject};
    use haedal::hawal_common::{Self, assert_gt, assert_lt};
    use std::debug;
    use walrus::{
        staking::Staking,
        test_utils,
        staked_wal::{Self,StakedWal},
        system::System
    };
    use haedal::hawal::{HAWAL};
    use sui::coin::{Self, Coin, TreasuryCap};
    use sui::object;
    use sui::clock::{Self,Clock};
    
    const EPOCH_DURATION: u64 = 14 * 24 * 60 * 60 * 1000;
    const PARAM_SELECTION_DELTA: u64 = (14 * 24 * 60 * 60 * 1000) / 2;

    const MIN_STAKING_THRESHOLD: u64 = 1_000_000_000; // 1 WAL

    const ADMIN_ADDR:address = @0xA11CE;//admin
    const TEST_STAKER_1: address = @0x1234;
    const TEST_STAKER_2: address = @0x1235;
    const TEST_STAKER_3: address = @0x1236;
    const TEST_STAKER_4: address = @0x1237;
    const TEST_STAKER_5: address = @0x1238;

    // Scenario: node users N+1 N+2
    // Epoch 0: uA stakes 1000 N+1
    // Epoch 1: uB stakes 2000 N+1
    // Epoch 1: uC stakes 3000 N+2
    // Epoch 1: uA Unstakes 1000 N+2
    // Epoch 2: uB Unstakes 2000 N+1
    // Epoch 3: uA cliam 1000
    // Epoch 3: uB cliam 2000
    // Epoch 3: uC Unstakes 3000 N+1
    // Epoch 4: uC cliam 3000
    #[test]
    fun test_main_for_one_node(){
        
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
         // nodes
        let mut excluded_node = nodes.pop_back();
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);

        let wal_scenario = runner.scenario();
        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        // uA stake1000
        hawal_common::print(b"ua stake");
        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(1000, ctx);
        let hawal_1 = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);
        let data = walstaking::get_total_staked(&mut staking_object);
        assert!(walstaking::get_total_staked(&mut staking_object) == MIN_STAKING_THRESHOLD * 1000, 111);

        // epoch1 n+1
        hawal_common::print(b"epoch1");
        wal_scenario.next_tx(ADMIN_ADDR);
        // 1.1
        clock_object.increment_for_testing(EPOCH_DURATION + 1000);
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        debug::print(&epoch);

        // uB stake2000
        hawal_common::print(b"ub stake");
        wal_scenario.next_tx(TEST_STAKER_2);
        let ctx = scenario.ctx();
        let coin2 = test_utils::mint_wal(2000 , ctx);
        let hawal_2 = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin2, excluded_node.node_id(), ctx);
        assert!(walstaking::get_total_staked(&staking_object) == MIN_STAKING_THRESHOLD * 3000, 222);

        // epoch1 n+2
        clock_object.increment_for_testing(PARAM_SELECTION_DELTA);
        // 1.6
        debug::print(&epoch);
        
        // uC stake3000
        hawal_common::print(b"uc stake");
        wal_scenario.next_tx(TEST_STAKER_3);
        let ctx = scenario.ctx();
        let coin3 = test_utils::mint_wal(3000 , ctx);
        let hawal_3 = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin3, excluded_node.node_id(), ctx);
        assert!(walstaking::get_total_staked(&staking_object) == 6000 * MIN_STAKING_THRESHOLD, 333);
        hawal_common::walrus_return_shared(wal_staking,wal_system);
        
        // uA
        hawal_common::print(b"backup stake");
        runner.tx!(TEST_STAKER_5, |staking, system, ctx| {
            let coin5 = test_utils::mint_wal(50 , ctx);
            interface::request_stake(staking, &mut staking_object, coin5, excluded_node.node_id(), ctx);
        });

        hawal_common::print(b"ua unstake");
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, hawal_1, ctx);
        });
        runner.scenario().next_tx(TEST_STAKER_1);
        let ticket_1 = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();

        runner.tx!(ADMIN_ADDR, |_, system, ctx| {
            let mut coin = test_utils::mint_wal(1000, ctx);
            let storage = system.reserve_space(1_000_000_000, 10, &mut coin, ctx);
            transfer::public_transfer(storage, ctx.sender());
            transfer::public_transfer(coin, ctx.sender());
        });

        // epoch2
        hawal_common::print(b"epoch2");
        // 2.4
        clock_object.increment_for_testing(EPOCH_DURATION - 2000);
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(excluded_node.sui_address(), |staking, _, _| {
            staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        });
        debug::print(&epoch);
        

        // uB 
        hawal_common::print(b"ub unstake");
        runner.tx!(TEST_STAKER_2, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, hawal_2, ctx);
        });
        runner.scenario().next_tx(TEST_STAKER_2);
        let ticket_2 = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();
        // assert!(walstaking::get_total_unstaked(&mut staking_object) == MIN_STAKING_THRESHOLD, 111);

        // epoch3
        hawal_common::print(b"epoch3");
        // 3.5
        clock_object.increment_for_testing(EPOCH_DURATION + 1000);
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(excluded_node.sui_address(), |staking, _, _| {
            staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        });
        debug::print(&epoch);
        

        // uA cliam
        hawal_common::print(b"ua claim");
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            interface::claim(system,staking, &mut staking_object,&clock_object, ticket_1, ctx);
        });

        // uB cliam
        hawal_common::print(b"ub claim");
        runner.tx!(TEST_STAKER_2, |staking, system, ctx| {
            interface::claim(system,staking, &mut staking_object,&clock_object, ticket_2, ctx);
        });

        // uC 
        hawal_common::print(b"uc unstake");
        runner.tx!(TEST_STAKER_3, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, hawal_3, ctx);
        });
        runner.scenario().next_tx(TEST_STAKER_3);
        let ticket_3 = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();

        // epoch4
        hawal_common::print(b"epoch4");
        clock_object.increment_for_testing(EPOCH_DURATION);
        // 4.5
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(excluded_node.sui_address(), |staking, _, _| {
            staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        });
        debug::print(&epoch);
        

        // uC calim
        hawal_common::print(b"uc claim");
        runner.tx!(TEST_STAKER_3, |staking, system, ctx| {
            interface::claim(system,staking, &mut staking_object,&clock_object, ticket_3, ctx);
        });

        // destory
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    // Scenario: nodes and users N+1 N+2
    // Epoch 0: uA stakes 1000 N+1 node1
    // Epoch 1: uB stakes 2000 N+1 node1
    // Epoch 1: uC stakes 3000 N+2 node2
    // Epoch 1: uD stakes 3000 N+2 node3
    // Epoch 1: uA Unstakes 1000 N+2
    // Epoch 2: uB Unstakes 2000 N+1
    // Epoch 2: uE stakes 5000 N+2 node3
    // Epoch 3: uA cliam 1000
    // Epoch 3: uB cliam 2000
    // Epoch 3: uC Unstakes 3000 N+1
    // Epoch 3: uD Unstakes 3000 N+1
    // Epoch 4: uC cliam 3000
    // Epoch 4: uD cliam 3000
    #[test]
    fun test_main_for_multi_node(){
        
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);

        let mut excluded_node_1 = nodes.pop_back();
        let mut excluded_node_2 = nodes.pop_back();
        let mut excluded_node_3 = nodes.pop_back();
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node_1.node_id(),excluded_node_2.node_id(),excluded_node_3.node_id()]);

        let wal_scenario = runner.scenario();
        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        // uA 
        hawal_common::print(b"ua stake");
        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(1000 , ctx);
        let hawal_1 = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin, excluded_node_1.node_id(), ctx);
        assert!(walstaking::get_total_staked(&mut staking_object) == MIN_STAKING_THRESHOLD * 1000, 111);

        // epoch1 n+1
        hawal_common::print(b"epoch1");
        wal_scenario.next_tx(ADMIN_ADDR);
        // 1.1
        clock_object.increment_for_testing(EPOCH_DURATION + 1000);
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node_1.cap_mut(), epoch, &clock_object);
        debug::print(&epoch);

        // uB 2000
        hawal_common::print(b"ub stake");
        wal_scenario.next_tx(TEST_STAKER_2);
        let ctx = scenario.ctx();
        let coin2 = test_utils::mint_wal(2000 , ctx);
        let hawal_2 = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin2, excluded_node_1.node_id(), ctx);
        assert!(walstaking::get_total_staked(&mut staking_object) == MIN_STAKING_THRESHOLD * 3000, 222);

        // epoch1 n+2  1.6
        clock_object.increment_for_testing(PARAM_SELECTION_DELTA);
        debug::print(&epoch);
        
        // uC 3000
        hawal_common::print(b"uc stake");
        wal_scenario.next_tx(TEST_STAKER_3);
        let ctx = scenario.ctx();
        let coin3 = test_utils::mint_wal(3000 , ctx);
        let hawal_3 = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin3, excluded_node_2.node_id(), ctx);
        let total = walstaking::get_total_staked(&staking_object);
        assert!(walstaking::get_total_staked(&staking_object) == MIN_STAKING_THRESHOLD * 6000, 444);

        // uD 3000
        hawal_common::print(b"uD stake");
        wal_scenario.next_tx(TEST_STAKER_4);
        let ctx = scenario.ctx();
        let coin4 = test_utils::mint_wal(3000 , ctx);
        let hawal_4 = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin4, excluded_node_3.node_id(), ctx);
        assert!(walstaking::get_total_staked(&staking_object) == MIN_STAKING_THRESHOLD * 9000, 333);
        hawal_common::walrus_return_shared(wal_staking,wal_system);

        // n+1
        runner.tx!(ADMIN_ADDR, |_, system, ctx| {
            let mut coin = test_utils::mint_wal(1000, ctx);
            let storage = system.reserve_space(1_000_000_000, 10, &mut coin, ctx);
            transfer::public_transfer(storage, ctx.sender());
            transfer::public_transfer(coin, ctx.sender());
        });
        
        hawal_common::print(b"backup stake");
        runner.tx!(TEST_STAKER_5, |staking, system, ctx| {
            let coin5 = test_utils::mint_wal(50 , ctx);
            interface::request_stake(staking, &mut staking_object, coin5, excluded_node_2.node_id(), ctx);
        });

        // uA 
        hawal_common::print(b"ua unstake");
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, hawal_1, ctx);
        });
        runner.scenario().next_tx(TEST_STAKER_1);
        let ticket_1 = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();

        // epoch2 // 2.4
        hawal_common::print(b"epoch2");
        clock_object.increment_for_testing(EPOCH_DURATION - 2000);
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        
        runner.tx!(ADMIN_ADDR, |staking, _, _| {
            staking.epoch_sync_done(excluded_node_1.cap_mut(), epoch, &clock_object);
            staking.epoch_sync_done(excluded_node_2.cap_mut(), epoch, &clock_object);
            staking.epoch_sync_done(excluded_node_3.cap_mut(), epoch, &clock_object);
        });
        debug::print(&epoch);

        runner.tx!(ADMIN_ADDR, |staking, system, ctx| {
            operate::update_validator_rewards(&operate_cap, &mut staking_object, system, staking, excluded_node_1.node_id(), ctx);
            operate::update_validator_rewards(&operate_cap, &mut staking_object, system, staking, excluded_node_2.node_id(), ctx);
            operate::update_validator_rewards(&operate_cap, &mut staking_object, system, staking, excluded_node_3.node_id(), ctx);
        });

        // uB 
        hawal_common::print(b"ub unstake");
        runner.tx!(TEST_STAKER_2, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, hawal_2, ctx);
        });
        runner.scenario().next_tx(TEST_STAKER_2);
        let ticket_2 = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();

        // epoch3 // 3.5
        hawal_common::print(b"epoch3");
        clock_object.increment_for_testing(EPOCH_DURATION + 1000);
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(ADMIN_ADDR, |staking, _, _| {
            staking.epoch_sync_done(excluded_node_2.cap_mut(), epoch, &clock_object);
            staking.epoch_sync_done(excluded_node_3.cap_mut(), epoch, &clock_object);
        });
        debug::print(&epoch);
        
        runner.tx!(ADMIN_ADDR, |staking, system, ctx| {
            operate::update_validator_rewards(&operate_cap, &mut staking_object, system, staking, excluded_node_1.node_id(), ctx);
            operate::update_validator_rewards(&operate_cap, &mut staking_object, system, staking, excluded_node_2.node_id(), ctx);
            operate::update_validator_rewards(&operate_cap, &mut staking_object, system, staking, excluded_node_3.node_id(), ctx);
        });

        // uA cliam
        hawal_common::print(b"ua claim");
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            interface::claim(system,staking, &mut staking_object,&clock_object, ticket_1, ctx);
        });

        // uC 
        hawal_common::print(b"uc unstake");
        runner.tx!(TEST_STAKER_3, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, hawal_3, ctx);
        });
        runner.scenario().next_tx(TEST_STAKER_3);
        let ticket_3 = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();

        // uD 
        hawal_common::print(b"uD unstake");
        runner.tx!(TEST_STAKER_4, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system,staking, &mut staking_object, &clock_object, hawal_4, ctx);
        });
        runner.scenario().next_tx(TEST_STAKER_4);
        let ticket_4 = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();

        // epoch4  4.5
        hawal_common::print(b"epoch4");
        clock_object.increment_for_testing(EPOCH_DURATION);
        
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(ADMIN_ADDR, |staking, _, _| {
            staking.epoch_sync_done(excluded_node_3.cap_mut(), epoch, &clock_object);
        });
        debug::print(&epoch);

        runner.tx!(ADMIN_ADDR, |staking, system, ctx| {
            operate::update_validator_rewards(&operate_cap, &mut staking_object, system, staking, excluded_node_1.node_id(), ctx);
            operate::update_validator_rewards(&operate_cap, &mut staking_object, system, staking, excluded_node_2.node_id(), ctx);
            operate::update_validator_rewards(&operate_cap, &mut staking_object, system, staking, excluded_node_3.node_id(), ctx);
        });
        
        // uB cliam
        hawal_common::print(b"ub claim");
        runner.tx!(TEST_STAKER_2, |staking, system, ctx| {
            interface::claim(system,staking, &mut staking_object,&clock_object, ticket_2, ctx);
        });

        // uC calim
        hawal_common::print(b"uc claim");
        runner.tx!(TEST_STAKER_3, |staking, system, ctx| {
            interface::claim(system,staking, &mut staking_object,&clock_object, ticket_3, ctx);
        });

        // uD calim
        hawal_common::print(b"uD claim");
        runner.tx!(TEST_STAKER_4, |staking, system, ctx| {
            interface::claim(system,staking, &mut staking_object,&clock_object, ticket_4, ctx);
        });

        // destory
        excluded_node_2.destroy();
        excluded_node_3.destroy();
        hawal_common::walrus_destroy(nodes,excluded_node_1,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }

    
    #[test]
    fun test_rewards_main(){
        
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;

        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();

        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        let mut excluded_node = nodes.pop_back();
        
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);

        
        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(5, ctx);
        interface::request_stake(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);
        hawal_common::walrus_return_shared(wal_staking,wal_system);

        hawal_common::print(b"epoch1");
        clock_object.increment_for_testing(EPOCH_DURATION);
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(excluded_node.sui_address(), |staking, _, _| {
            staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        });
        
        runner.tx!(ADMIN_ADDR, |_, system, ctx| {
            let mut coin = test_utils::mint_wal(1000, ctx);
            let storage = system.reserve_space(1_000_000_000, 10, &mut coin, ctx);
            transfer::public_transfer(storage, ctx.sender());
            transfer::public_transfer(coin, ctx.sender());
        });

        
        hawal_common::print(b"rewards 0");
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            let rewards = staking.calculate_rewards(excluded_node.node_id(), MIN_STAKING_THRESHOLD * 5, epoch, epoch +1);
            debug::print(&rewards);
        });

        clock_object.increment_for_testing(EPOCH_DURATION);
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(excluded_node.sui_address(), |staking, _, _| {
            staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        });

        
        hawal_common::print(b"rewards >0");
        runner.tx!(TEST_STAKER_1, |staking, system, _| {
            let rewards = staking.calculate_rewards(excluded_node.node_id(), MIN_STAKING_THRESHOLD * 5, 1, epoch);
            debug::print(&rewards);
        });

        // destory
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }


    #[test]
    fun test_long_epoch_cycle() {
        let (mut scenario_val, mut staking_object, admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;
        let (mut nodes, mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        
        // node
        let mut excluded_node = nodes.pop_back();
        operate::set_active_validators(&operate_cap, &mut staking_object, vector[excluded_node.node_id()]);

        let wal_scenario = runner.scenario();
        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
        // Ua stake 1000
        hawal_common::print(b"user A stakes 1000");
        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(1000, ctx);
        let hawal_1 = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin, excluded_node.node_id(), ctx);
        assert!(walstaking::get_total_staked(&mut staking_object) == MIN_STAKING_THRESHOLD * 1000, 111);

        // epoch1
        hawal_common::print(b"enter epoch 1");
        wal_scenario.next_tx(ADMIN_ADDR);
        clock_object.increment_for_testing(EPOCH_DURATION + 1000);
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        debug::print(&epoch);
        
        // epcoh2.2
        hawal_common::print(b"enter epoch 2 first half");
        clock_object.increment_for_testing(PARAM_SELECTION_DELTA);
        
        hawal_common::print(b"user A unstakes in epoch 2 first half");
        hawal_common::walrus_return_shared(wal_staking, wal_system);
        
        runner.tx!(ADMIN_ADDR, |_, system, ctx| {
            let mut coin = test_utils::mint_wal(1000, ctx);
            let storage = system.reserve_space(1_000_000_000, 10, &mut coin, ctx);
            transfer::public_transfer(storage, ctx.sender());
            transfer::public_transfer(coin, ctx.sender());
        });
        
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system, staking, &mut staking_object, &clock_object, hawal_1, ctx);
        });
        runner.scenario().next_tx(TEST_STAKER_1);
        let ticket_1 = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();
        

        hawal_common::print(b"enter epoch 2 second half");
        clock_object.increment_for_testing(PARAM_SELECTION_DELTA);
        
        // epoch 3
        hawal_common::print(b"enter epoch 3");
        clock_object.increment_for_testing(EPOCH_DURATION);
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(excluded_node.sui_address(), |staking, _, _| {
            staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        });
        debug::print(&epoch);
        
        // Ua claim
        hawal_common::print(b"user A claims in epoch 3");
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            interface::claim(system, staking, &mut staking_object, &clock_object, ticket_1, ctx);
        });
        
        // 
        hawal_common::print(b"simulate 65535 epochs");
        let mut i = 0;
        while (i < 65535) {
            clock_object.increment_for_testing(EPOCH_DURATION);
            runner.tx!(ADMIN_ADDR, |staking, system, _| {
                staking.voting_end(&clock_object);
                staking.initiate_epoch_change(system, &clock_object);
            });
            let epoch = runner.epoch();
            runner.tx!(excluded_node.sui_address(), |staking, _, _| {
                staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
            });
            i = i + 1;
            if (i % 10000 == 0) {
                debug::print(&i);
            }
        }
        debug::print(&runner.epoch());
        
        // Ua stake
        hawal_common::print(b"user A stakes again");
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            let coin = test_utils::mint_wal(1000, ctx);
            let hawal = interface::request_stake(staking, &mut staking_object, coin, excluded_node.node_id(), ctx);
            transfer::public_transfer(hawal, ctx.sender());
        });
        
        // epoch +1
        hawal_common::print(b"epoch +1");
        clock_object.increment_for_testing(EPOCH_DURATION);
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(excluded_node.sui_address(), |staking, _, _| {
            staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        });
        debug::print(&epoch);
        
        // Ua unstake
        hawal_common::print(b"user A unstakes");
        runner.scenario().next_tx(TEST_STAKER_1);
        let hawal_2 = runner.scenario().take_from_sender<HAWAL>();
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system, staking, &mut staking_object, &clock_object, hawal_2, ctx);
        });
        
        hawal_common::walrus_destroy(nodes, excluded_node, runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }
    
    #[test]
    fun test_two_nodes_staking() {
        let (mut scenario_val, mut staking_object, admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;
        let (mut nodes, mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        
        // node q
        let mut node_q = nodes.pop_back();
        operate::set_active_validators(&operate_cap, &mut staking_object, vector[node_q.node_id()]);
        
        let wal_scenario = runner.scenario();
        wal_scenario.next_tx(ADMIN_ADDR);
        let mut wal_staking = wal_scenario.take_shared<Staking>();
        let mut wal_system = wal_scenario.take_shared<System>();
        
         hawal_common::print(b"epoch + 0.5");
        clock_object.increment_for_testing(PARAM_SELECTION_DELTA);

        // Ua stake
        hawal_common::print(b"user A stakes 1000 to node Q");
        wal_scenario.next_tx(TEST_STAKER_1);
        let ctx = scenario.ctx();
        let coin = test_utils::mint_wal(1000, ctx);
        let hawal_1 = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin, node_q.node_id(), ctx);
        assert!(walstaking::get_total_staked(&mut staking_object) == MIN_STAKING_THRESHOLD * 1000, 111);
        
        // epoch + 1
        hawal_common::print(b"epoch1");
        wal_scenario.next_tx(ADMIN_ADDR);
        // 1.1
        clock_object.increment_for_testing(EPOCH_DURATION + 1000);
        wal_staking.voting_end(&clock_object);
        wal_staking.initiate_epoch_change(&mut wal_system, &clock_object);
        let epoch = wal_system.epoch();
        wal_staking.epoch_sync_done(node_q.cap_mut(), epoch, &clock_object);
        debug::print(&epoch);
        
        // Ub stake
        hawal_common::print(b"user B stakes 2000 to node W");
        wal_scenario.next_tx(TEST_STAKER_2);
        let ctx = scenario.ctx();
        let coin2 = test_utils::mint_wal(2000, ctx);
        let hawal_2 = walstaking::request_stake_coin(&mut wal_staking, &mut staking_object, coin2, node_q.node_id(), ctx);
        assert!(walstaking::get_total_staked(&mut staking_object) == MIN_STAKING_THRESHOLD * 3000, 222);
        hawal_common::walrus_return_shared(wal_staking, wal_system);
        
        runner.tx!(TEST_STAKER_5, |staking, system, ctx| {
            let coin5 = test_utils::mint_wal(50 , ctx);
            interface::request_stake(staking, &mut staking_object, coin5, node_q.node_id(), ctx);
        });

        // epoch + 0.5
        hawal_common::print(b"epoch + 0.5");
        clock_object.increment_for_testing(PARAM_SELECTION_DELTA);
        
        runner.tx!(ADMIN_ADDR, |_, system, ctx| {
            let mut coin = test_utils::mint_wal(1000, ctx);
            let storage = system.reserve_space(1_000_000_000, 10, &mut coin, ctx);
            transfer::public_transfer(storage, ctx.sender());
            transfer::public_transfer(coin, ctx.sender());
        });
        
        
        // epoch + 0.5
        hawal_common::print(b"epoch + 0.5");
        clock_object.increment_for_testing(PARAM_SELECTION_DELTA);
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(ADMIN_ADDR, |staking, _, _| {
            staking.epoch_sync_done(node_q.cap_mut(), epoch, &clock_object);
        });
        debug::print(&epoch);


        // epoch + 1
        hawal_common::print(b"epoch 3");
        clock_object.increment_for_testing(EPOCH_DURATION);
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(ADMIN_ADDR, |staking, _, _| {
            staking.epoch_sync_done(node_q.cap_mut(), epoch, &clock_object);
        });
        debug::print(&epoch);

        // UA unstake N +2
        hawal_common::print(b"user A unstakes");
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system, staking, &mut staking_object, &clock_object, hawal_1, ctx);
        });

        runner.scenario().next_tx(TEST_STAKER_1);
        let ticket_1 = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();
        // std::debug::print(&ticket_1);

        // Ub N+1
        hawal_common::print(b"user B unstakes");
        runner.tx!(TEST_STAKER_2, |staking, system, ctx| {
            walstaking::request_withdraw_stake(system, staking, &mut staking_object, &clock_object, hawal_2, ctx);
        });
        runner.scenario().next_tx(TEST_STAKER_2);
        let ticket_2 = runner.scenario().take_from_sender<walstaking::UnstakeTicket>();
        
        

        // epcoh + 1
        hawal_common::print(b"enter next epoch for claiming");
        clock_object.increment_for_testing(EPOCH_DURATION);
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(ADMIN_ADDR, |staking, _, _| {
            // staking.epoch_sync_done(node_q.cap_mut(), epoch, &clock_object);
            staking.epoch_sync_done(node_q.cap_mut(), epoch, &clock_object);
        });
        debug::print(&epoch);

        // epcoh + 1
        hawal_common::print(b"enter next epoch for claiming");
        clock_object.increment_for_testing(EPOCH_DURATION);
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(ADMIN_ADDR, |staking, _, _| {
            // staking.epoch_sync_done(node_q.cap_mut(), epoch, &clock_object);
            staking.epoch_sync_done(node_q.cap_mut(), epoch, &clock_object);
        });
        debug::print(&epoch);
        
        // Ua claim Ub claim
        hawal_common::print(b"users A and B claim");
        runner.tx!(TEST_STAKER_1, |staking, system, ctx| {
            interface::claim(system, staking, &mut staking_object, &clock_object, ticket_1, ctx);
        });
        
        runner.tx!(TEST_STAKER_2, |staking, system, ctx| {
            interface::claim(system, staking, &mut staking_object, &clock_object, ticket_2, ctx);
        });
        
        hawal_common::walrus_destroy(nodes, node_q, runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }
}
