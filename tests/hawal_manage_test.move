#[test_only]
module haedal::test_manage {
    use sui::clock;
    use sui::coin;
    use std::debug;
    use sui::test_scenario::{<PERSON>, <PERSON><PERSON><PERSON>};
    use haedal::{
        walstaking,
        manage,
        operate,
        interface,
        config,
        hawal
    };
    use walrus::{
        staking::Staking,
        test_utils,
        staked_wal::{Self,StakedWal},
        system::System
    };
    use haedal::hawal_common;

    const ADMIN_ADDR:address = @0x1234;//admin
    const BACKUP_ADDR:address = @0x6666;//operate
    const EPOCH_DURATION: u64 = 2 * 24 * 60 * 60 * 1000;

    
    #[test]
    fun test_configs() {
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;
        
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
        let wal_scenario = runner.scenario();
        let mut excluded_node = nodes.pop_back();
        
        // test config setters
        test_scenario::next_tx(scenario, ADMIN_ADDR);
        {
            manage::set_operator_cap_to_address(&admin_cap,ADMIN_ADDR, test_scenario::ctx(scenario));
        };

        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }


    #[test]
    fun test_collect_rewards_fee() {
        let (mut scenario_val,mut staking_object,admin_cap, operate_cap, mut clock_object) = hawal_common::hawal_test_setup(ADMIN_ADDR);
        let scenario = &mut scenario_val;
        let (mut nodes,mut runner) = hawal_common::walrus_test_setup(ADMIN_ADDR);
         // nodes
        let mut excluded_node = nodes.pop_back();
        operate::set_active_validators(&operate_cap, &mut staking_object,vector[excluded_node.node_id()]);

        hawal_common::print(b"backup stake");
        runner.tx!(BACKUP_ADDR, |staking, system, ctx| {
            let coin5 = test_utils::mint_wal(50 , ctx);
            interface::request_stake(staking, &mut staking_object, coin5, excluded_node.node_id(), ctx);
        });

        // request_collect_rewards_fee
        hawal_common::print(b"request_collect_rewards_fee");
        runner.tx!(ADMIN_ADDR, |staking, system, ctx| {
            manage::request_collect_rewards_fee(&admin_cap,system,staking, &mut staking_object, ctx);
        });

        // epoch + 2
        hawal_common::print(b"epoch2");
        // 2.4
        clock_object.increment_for_testing(EPOCH_DURATION * 2 +100);
        runner.tx!(ADMIN_ADDR, |staking, system, _| {
            staking.voting_end(&clock_object);
            staking.initiate_epoch_change(system, &clock_object);
        });
        let epoch = runner.epoch();
        runner.tx!(excluded_node.sui_address(), |staking, _, _| {
            staking.epoch_sync_done(excluded_node.cap_mut(), epoch, &clock_object);
        });
        debug::print(&epoch);
        
        // claim_collect_rewards_fee
        hawal_common::print(b"claim_collect_rewards_fee");
        runner.tx!(ADMIN_ADDR, |staking, system, ctx| {
            manage::claim_collect_rewards_fee(&admin_cap,system,staking, &mut staking_object, ADMIN_ADDR, ctx);
        });

        // destory
        hawal_common::walrus_destroy(nodes,excluded_node,runner);
        hawal_common::hawal_test_tear_down(scenario_val, staking_object, admin_cap, operate_cap, clock_object);
    }
}
