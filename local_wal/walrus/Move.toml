[package]
name = "Walrus"
license = "Apache-2.0"
authors = ["Mysten Labs <<EMAIL>>"]
edition = "2024.beta"
published-at = "0xfa65cb2d62f4d39e60346fb7d501c12538ca2bbc646eaa37ece2aec5f897814e"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "testnet-v1.50.1" }
WAL = { local = "../wal" }

[addresses]
walrus = "0xfdc88f7d7cf30afab2f82e8380d11ee8f70efb90e863d1de8616fae1bb09ea77"
