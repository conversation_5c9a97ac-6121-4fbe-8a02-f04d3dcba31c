// Copyright (c) Walrus Foundation
// SPDX-License-Identifier: Apache-2.0

module walrus::init;

use std::type_name;
use sui::{clock::Clock, package::{Self, Publisher, UpgradeCap}};
use walrus::{display, events, staking::{Self, Staking}, system::{Self, System}, upgrade};

// Error codes
// Error types in `walrus-sui/types/move_errors.rs` are auto-generated from the Move error codes.
/// Error during the migration to the new system/staking object versions.
const EInvalidMigration: u64 = 0;
/// The provided upgrade cap does not belong to this package.
const EInvalidUpgradeCap: u64 = 1;

/// The OTW to create `Publisher` and `Display` objects.
public struct INIT has drop {}

/// Must only be created by `init`.
public struct InitCap has key, store {
    id: UID,
    publisher: Publisher,
}

/// Initializes the system by creating an init cap and transferring it to the sender.
///
/// This allows the sender to call the function to actually initialize the system
/// with the corresponding parameters. Once that function is called, the cap is destroyed.
fun init(otw: INIT, ctx: &mut TxContext) {
    let id = object::new(ctx);
    let publisher = package::claim(otw, ctx);
    let init_cap = InitCap { id, publisher };
    transfer::transfer(init_cap, ctx.sender());
}

/// Initializes Walrus and shares the system and staking objects.
///
/// This can only be called once, after which the `InitCap` is destroyed.
public fun initialize_walrus(
    init_cap: InitCap,
    upgrade_cap: UpgradeCap,
    epoch_zero_duration: u64,
    epoch_duration: u64,
    n_shards: u16,
    max_epochs_ahead: u32,
    clock: &Clock,
    ctx: &mut TxContext,
): upgrade::EmergencyUpgradeCap {
    let InitCap { id, publisher } = init_cap;
    id.delete();
    let package_id = upgrade_cap.package();
    assert!(
        type_name::get<InitCap>().get_address() == package_id.to_address().to_ascii_string(),
        EInvalidUpgradeCap,
    );
    system::create_empty(max_epochs_ahead, package_id, ctx);
    staking::create(epoch_zero_duration, epoch_duration, n_shards, package_id, clock, ctx);
    display::create(publisher, ctx);
    let emergency_upgrade_cap = upgrade::new(upgrade_cap, ctx);
    emergency_upgrade_cap
}

/// Migrates the staking and system objects to the new package ID.
///
/// This must be called in the new package after an upgrade is committed
/// to emit an event that informs all storage nodes and prevent previous package
/// versions from being used.
public fun migrate(staking: &mut Staking, system: &mut System) {
    staking.migrate();
    system.migrate();
    // Check that the package id and version are the same.
    assert!(staking.package_id() == system.package_id(), EInvalidMigration);
    assert!(staking.version() == system.version(), EInvalidMigration);
    // Emit an event to inform storage nodes of the upgrade.
    events::emit_contract_upgraded(
        staking.epoch(),
        staking.package_id(),
        staking.version(),
    );
}

// === Test only ===

#[test_only]
public fun init_for_testing(ctx: &mut TxContext) {
    init(INIT {}, ctx);
}

#[test_only]
/// Does the same as `initialize_walrus` but does not check the package id of the upgrade cap.
///
/// This is needed for testing, since the package ID of all types will be zero, which cannot be used
/// as the package ID for an upgrade cap.
public fun initialize_for_testing(
    init_cap: InitCap,
    upgrade_cap: UpgradeCap,
    epoch_zero_duration: u64,
    epoch_duration: u64,
    n_shards: u16,
    max_epochs_ahead: u32,
    clock: &Clock,
    ctx: &mut TxContext,
): upgrade::EmergencyUpgradeCap {
    let InitCap { id, publisher } = init_cap;
    id.delete();
    let package_id = upgrade_cap.package();
    system::create_empty(max_epochs_ahead, package_id, ctx);
    staking::create(epoch_zero_duration, epoch_duration, n_shards, package_id, clock, ctx);
    display::create(publisher, ctx);
    let emergency_upgrade_cap = upgrade::new(upgrade_cap, ctx);
    emergency_upgrade_cap
}
