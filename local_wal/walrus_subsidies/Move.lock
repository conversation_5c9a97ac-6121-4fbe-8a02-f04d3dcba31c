# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "FA870B23BB1F3A59D7DFAF3B7BE50D6EF504291432CBED4BC544024C020AAAB6"
deps_digest = "060AD7E57DFB13104F21BE5F5C3759D03F0553FC3229247D9A7A6B45F50D03A3"
dependencies = [
  { id = "Sui", name = "Sui" },
  { id = "WAL", name = "WAL" },
  { id = "Walrus", name = "Walrus" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "testnet-v1.50.1", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "testnet-v1.50.1", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[[move.package]]
id = "WAL"
source = { local = "../wal" }

dependencies = [
  { id = "Sui", name = "Sui" },
]

[[move.package]]
id = "Walrus"
source = { local = "../walrus" }

dependencies = [
  { id = "Sui", name = "Sui" },
  { id = "WAL", name = "WAL" },
]

[move.toolchain-version]
compiler-version = "1.50.1"
edition = "2024.beta"
flavor = "sui"
