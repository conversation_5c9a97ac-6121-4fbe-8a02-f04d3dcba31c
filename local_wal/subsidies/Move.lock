# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "FDB57BEED108EE7FC9A42405078A5EE89943E3567ED410E8EC005F588D9F35C9"
deps_digest = "060AD7E57DFB13104F21BE5F5C3759D03F0553FC3229247D9A7A6B45F50D03A3"
dependencies = [
  { id = "Sui", name = "Sui" },
  { id = "WAL", name = "WAL" },
  { id = "Walrus", name = "Walrus" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "testnet-v1.50.1", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "testnet-v1.50.1", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[[move.package]]
id = "WAL"
source = { local = "../wal" }

dependencies = [
  { id = "Sui", name = "Sui" },
]

[[move.package]]
id = "Walrus"
source = { local = "../walrus" }

dependencies = [
  { id = "Sui", name = "Sui" },
  { id = "WAL", name = "WAL" },
]

[move.toolchain-version]
compiler-version = "1.50.1"
edition = "2024.beta"
flavor = "sui"

[env]

[env.mainnet]
chain-id = "35834a8a"
original-published-id = "0xd843c37d213ea683ec3519abe4646fd618f52d7fce1c4e9875a4144d53e21ebc"
latest-published-id = "0xd843c37d213ea683ec3519abe4646fd618f52d7fce1c4e9875a4144d53e21ebc"
published-version = "1"
