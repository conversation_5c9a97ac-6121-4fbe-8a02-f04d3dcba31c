# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "80A026835683A6C1D4F7E24881BA4748E8519E576E168876CF8148F72AD8C762"
deps_digest = "3C4103934B1E040BB6B23F1D610B4EF9F2F1166A50A104EADCF77467C004C600"
dependencies = [
  { id = "Sui", name = "Sui" },
  { id = "WAL", name = "WAL" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "testnet-v1.49.1", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "testnet-v1.49.1", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[[move.package]]
id = "WAL"
source = { local = "../wal" }

dependencies = [
  { id = "Sui", name = "Sui" },
]

[move.toolchain-version]
compiler-version = "1.50.1"
edition = "2024.beta"
flavor = "sui"
